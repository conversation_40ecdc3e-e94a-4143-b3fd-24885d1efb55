/**
 * Componente di test per verificare il design system
 * Mostra i colori personalizzati e i componenti shadcn/ui
 */
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function DesignSystemTest() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-3xl font-bold font-geist">Design System Test</h1>
      
      {/* Test dei colori personalizzati */}
      <Card>
        <CardHeader>
          <CardTitle>Palette Colori Personalizzata</CardTitle>
          <CardDescription>Test dei colori definiti nel design system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-primary rounded-lg"></div>
              <span className="text-sm font-mono">Primary</span>
              <span className="text-xs text-muted-foreground">#DA1E28</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-secondary-blue rounded-lg"></div>
              <span className="text-sm font-mono">Secondary Blue</span>
              <span className="text-xs text-muted-foreground">#0074E1</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-secondary-yellow rounded-lg"></div>
              <span className="text-sm font-mono">Secondary Yellow</span>
              <span className="text-xs text-muted-foreground">#FFCE52</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-secondary-purple rounded-lg"></div>
              <span className="text-sm font-mono">Secondary Purple</span>
              <span className="text-xs text-muted-foreground">#B48CF1</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-secondary-green rounded-lg"></div>
              <span className="text-sm font-mono">Secondary Green</span>
              <span className="text-xs text-muted-foreground">#1DBA6C</span>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <div className="w-16 h-16 bg-accent-error rounded-lg"></div>
              <span className="text-sm font-mono">Accent Error</span>
              <span className="text-xs text-muted-foreground">#E32E31</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test dei font */}
      <Card>
        <CardHeader>
          <CardTitle>Test Tipografia</CardTitle>
          <CardDescription>Verifica dei font personalizzati</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="font-geist text-lg">Font Geist: The quick brown fox jumps over the lazy dog</p>
          </div>
          <div>
            <p className="font-inter text-lg">Font Inter: The quick brown fox jumps over the lazy dog</p>
          </div>
          <div>
            <p className="font-mono text-lg">Font Mono: The quick brown fox jumps over the lazy dog</p>
          </div>
        </CardContent>
      </Card>

      {/* Test dei componenti shadcn/ui */}
      <Card>
        <CardHeader>
          <CardTitle>Componenti shadcn/ui</CardTitle>
          <CardDescription>Test dei componenti base installati</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Button>Default Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="destructive">Destructive Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
