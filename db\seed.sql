-- db/seed.sql: dati demo multi-tenant per preview e test
-- Tenants (slug in codice, qui come commento)
INSERT INTO tenants(id, slug) VALUES
  (1, 'ua'),
  (2, 'it');

-- Categories (esempi)
INSERT INTO categories(id, name) VALUES
  (1, 'Materials'),
  (2, 'Labor');

-- Announcements – tenant ua
INSERT INTO announcements
  (id, tenant_id, creator_id, type, title, description, region, location,
   budget_min, budget_max, salary_range, availability, created_at)
VALUES
  (1, 1, '00000000-0000-0000-0000-000000000001', 'project',
   'Acquisto 500 t di cemento', 'Richiesta preventivo', 'Kyiv', NULL,
   10000, 25000, NULL, NULL, NOW()),
  (2, 1, '00000000-0000-0000-0000-000000000001', 'job',
   'Muratore esperto', 'Disponibile a Kharkiv', NULL, 'Kharkiv',
   NULL, NULL, '€1500-€1800', 'full-time', NOW());

-- Announcements – tenant it
INSERT INTO announcements
  (id, tenant_id, creator_id, type, title, description, region, location,
   budget_min, budget_max, salary_range, availability, created_at)
VALUES
  (3, 2, '00000000-0000-0000-0000-000000000002', 'project',
   'Fornitura laterizi', 'Richiesta 20 000 mattoni', 'Lazio', NULL,
   5000, 8000, NULL, NULL, NOW());
