# =============================================================================
# Secrets Management per ReBuild Link Infrastructure
# =============================================================================
# Questo file gestisce la sincronizzazione automatica dei secrets tra:
# - GitHub Actions (per CI/CD pipeline)
# - Vercel (per environment variables dell'applicazione)
# =============================================================================

# =============================================================================
# GitHub Actions Secrets
# =============================================================================
# Questi secrets vengono utilizzati dalla pipeline CI/CD in GitHub Actions

resource "github_actions_secret" "database_url" {
  repository      = var.github_repo
  secret_name     = "DATABASE_URL"
  plaintext_value = var.database_url
}

resource "github_actions_secret" "supabase_url" {
  repository      = var.github_repo
  secret_name     = "SUPABASE_URL"
  plaintext_value = var.supabase_url
}

resource "github_actions_secret" "supabase_anon_key" {
  repository      = var.github_repo
  secret_name     = "SUPABASE_ANON_KEY"
  plaintext_value = var.supabase_anon_key
}

# Secrets aggiuntivi per RDS monitoring (se necessari)
resource "github_actions_secret" "rds_instance_id" {
  repository      = var.github_repo
  secret_name     = "RDS_INSTANCE_ID"
  plaintext_value = var.db_instance_id
}

resource "github_actions_secret" "aws_region" {
  repository      = var.github_repo
  secret_name     = "AWS_DEFAULT_REGION"
  plaintext_value = var.aws_region
}

# =============================================================================
# Vercel Environment Variables
# =============================================================================
# Queste variabili vengono utilizzate dall'applicazione Next.js su Vercel

# Database connection per server-side
resource "vercel_environment_variable" "database_url" {
  project_id = var.vercel_project_id
  key        = "DATABASE_URL"
  value      = var.database_url
  target     = ["preview", "production"]
  type       = "encrypted"  # Crittografata su Vercel
}

# Supabase URL per client-side (pubblica)
resource "vercel_environment_variable" "supabase_url_public" {
  project_id = var.vercel_project_id
  key        = "NEXT_PUBLIC_SUPABASE_URL"
  value      = var.supabase_url
  target     = ["preview", "production"]
  type       = "plain"  # Pubblica (NEXT_PUBLIC_)
}

# Supabase URL per server-side (privata)
resource "vercel_environment_variable" "supabase_url" {
  project_id = var.vercel_project_id
  key        = "SUPABASE_URL"
  value      = var.supabase_url
  target     = ["preview", "production"]
  type       = "encrypted"
}

# Supabase anon key per client-side (pubblica)
resource "vercel_environment_variable" "supabase_anon_key_public" {
  project_id = var.vercel_project_id
  key        = "NEXT_PUBLIC_SUPABASE_ANON_KEY"
  value      = var.supabase_anon_key
  target     = ["preview", "production"]
  type       = "plain"  # Pubblica (NEXT_PUBLIC_)
}

# Supabase anon key per server-side (privata)
resource "vercel_environment_variable" "supabase_anon_key" {
  project_id = var.vercel_project_id
  key        = "SUPABASE_ANON_KEY"
  value      = var.supabase_anon_key
  target     = ["preview", "production"]
  type       = "encrypted"
}

# Node environment
resource "vercel_environment_variable" "node_env" {
  project_id = var.vercel_project_id
  key        = "NODE_ENV"
  value      = "production"
  target     = ["production"]
  type       = "plain"
}

resource "vercel_environment_variable" "node_env_preview" {
  project_id = var.vercel_project_id
  key        = "NODE_ENV"
  value      = "preview"
  target     = ["preview"]
  type       = "plain"
}

# Next.js telemetry (disabilitata per privacy)
resource "vercel_environment_variable" "next_telemetry" {
  project_id = var.vercel_project_id
  key        = "NEXT_TELEMETRY_DISABLED"
  value      = "1"
  target     = ["preview", "production"]
  type       = "plain"
}

# =============================================================================
# Outputs per verifica
# =============================================================================
output "github_secrets_created" {
  description = "Lista dei secrets GitHub creati"
  value = [
    github_actions_secret.database_url.secret_name,
    github_actions_secret.supabase_url.secret_name,
    github_actions_secret.supabase_anon_key.secret_name,
    github_actions_secret.rds_instance_id.secret_name,
    github_actions_secret.aws_region.secret_name
  ]
}

output "vercel_env_vars_created" {
  description = "Lista delle environment variables Vercel create"
  value = [
    vercel_environment_variable.database_url.key,
    vercel_environment_variable.supabase_url_public.key,
    vercel_environment_variable.supabase_url.key,
    vercel_environment_variable.supabase_anon_key_public.key,
    vercel_environment_variable.supabase_anon_key.key,
    vercel_environment_variable.node_env.key,
    vercel_environment_variable.next_telemetry.key
  ]
}

output "secrets_summary" {
  description = "Riepilogo della configurazione secrets"
  value = {
    github_repo           = var.github_repo
    vercel_project_id     = var.vercel_project_id
    github_secrets_count  = 5
    vercel_env_vars_count = 7
    targets              = ["preview", "production"]
  }
}
