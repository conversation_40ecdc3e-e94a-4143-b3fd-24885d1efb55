-- ============================================================
-- Migration: 001_initial_schema.sql
-- Data: 2025-01-09
-- Scopo: Schema iniziale per ReBuild Link - Marketplace multi-tenant
-- Autore: ReBuild Link Team
-- ------------------------------------------------------------
-- Questo file crea lo schema base per l'applicazione multi-tenant
-- ReBuild Link, includendo tabelle per tenants, utenti, annunci,
-- categorie e relazioni.
--
-- Caratteristiche:
--   • Multi-tenancy tramite tenant_id
--   • UUID per identificatori utente (Supabase)
--   • Indici per performance
--   • Vincoli di integrità referenziale
-- ============================================================

-- Abilita estensione UUID per generazione automatica
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 1. TENANTS - Gestione multi-tenancy
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE tenants (
    id SERIAL PRIMARY KEY,
    slug VARCHAR(50) UNIQUE NOT NULL,           -- Identificatore URL (ua, it, etc.)
    name VARCHAR(255) NOT NULL,                 -- Nome completo del tenant
    default_locale VARCHAR(5) DEFAULT 'en',    -- Lingua predefinita (en, ua, it)
    is_active BOOLEAN DEFAULT true,             -- Stato attivazione tenant
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance
CREATE INDEX idx_tenants_slug ON tenants(slug);
CREATE INDEX idx_tenants_active ON tenants(is_active);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 2. CATEGORIES - Categorie per classificazione annunci
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,                -- Nome categoria (Materials, Labor, etc.)
    description TEXT,                          -- Descrizione opzionale
    icon VARCHAR(50),                          -- Icona per UI (nome FontAwesome, etc.)
    is_active BOOLEAN DEFAULT true,            -- Stato attivazione categoria
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance
CREATE INDEX idx_categories_active ON categories(is_active);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 3. USERS - Profili utente (collegati a Supabase Auth)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE users (
    id UUID PRIMARY KEY,                       -- UUID da Supabase Auth
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,               -- Email utente (da Supabase)
    user_type VARCHAR(20) DEFAULT 'individual' CHECK (user_type IN ('individual', 'company', 'organization')),
    display_name VARCHAR(255),                 -- Nome visualizzato
    company_name VARCHAR(255),                 -- Nome azienda (se applicabile)
    phone VARCHAR(50),                         -- Telefono di contatto
    location VARCHAR(255),                     -- Località utente
    bio TEXT,                                  -- Biografia/descrizione
    avatar_url TEXT,                           -- URL avatar (Supabase Storage)
    is_verified BOOLEAN DEFAULT false,         -- Stato verifica account
    is_active BOOLEAN DEFAULT true,            -- Stato attivazione account
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance e ricerca
CREATE INDEX idx_users_tenant ON users(tenant_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_type ON users(user_type);
CREATE INDEX idx_users_location ON users(location);
CREATE INDEX idx_users_active ON users(is_active);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 4. ANNOUNCEMENTS - Annunci principali (progetti e lavori)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE announcements (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('project', 'job', 'service', 'material')),
    title VARCHAR(500) NOT NULL,               -- Titolo annuncio
    description TEXT NOT NULL,                 -- Descrizione dettagliata
    region VARCHAR(255),                       -- Regione geografica
    location VARCHAR(255),                     -- Località specifica
    
    -- Campi per progetti/materiali
    budget_min DECIMAL(12,2),                  -- Budget minimo
    budget_max DECIMAL(12,2),                  -- Budget massimo
    
    -- Campi per lavori
    salary_range VARCHAR(100),                 -- Range salariale (es. "€1500-€1800")
    availability VARCHAR(50),                  -- Disponibilità (full-time, part-time, etc.)
    
    -- Metadati
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled')),
    priority INTEGER DEFAULT 0,               -- Priorità per ordinamento
    expires_at TIMESTAMP,                      -- Data scadenza annuncio
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance e ricerca
CREATE INDEX idx_announcements_tenant ON announcements(tenant_id);
CREATE INDEX idx_announcements_creator ON announcements(creator_id);
CREATE INDEX idx_announcements_type ON announcements(type);
CREATE INDEX idx_announcements_status ON announcements(status);
CREATE INDEX idx_announcements_location ON announcements(location);
CREATE INDEX idx_announcements_created ON announcements(created_at DESC);
CREATE INDEX idx_announcements_budget ON announcements(budget_min, budget_max);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 5. ANNOUNCEMENT_CATEGORIES - Relazione many-to-many annunci-categorie
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE announcement_categories (
    announcement_id INTEGER NOT NULL REFERENCES announcements(id) ON DELETE CASCADE,
    category_id INTEGER NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    PRIMARY KEY (announcement_id, category_id)
);

-- Indici per performance
CREATE INDEX idx_announcement_categories_announcement ON announcement_categories(announcement_id);
CREATE INDEX idx_announcement_categories_category ON announcement_categories(category_id);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 6. MESSAGES - Sistema messaggistica tra utenti
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE messages (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    announcement_id INTEGER REFERENCES announcements(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subject VARCHAR(500),                      -- Oggetto messaggio
    content TEXT NOT NULL,                     -- Contenuto messaggio
    is_read BOOLEAN DEFAULT false,             -- Stato lettura
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance
CREATE INDEX idx_messages_tenant ON messages(tenant_id);
CREATE INDEX idx_messages_announcement ON messages(announcement_id);
CREATE INDEX idx_messages_sender ON messages(sender_id);
CREATE INDEX idx_messages_recipient ON messages(recipient_id);
CREATE INDEX idx_messages_unread ON messages(recipient_id, is_read);
CREATE INDEX idx_messages_created ON messages(created_at DESC);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 7. RATINGS - Sistema valutazioni tra utenti
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE ratings (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    announcement_id INTEGER REFERENCES announcements(id) ON DELETE CASCADE,
    rater_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rated_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,                              -- Commento opzionale
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Vincolo: un utente può valutare un altro utente solo una volta per annuncio
    UNIQUE(announcement_id, rater_id, rated_id)
);

-- Indici per performance
CREATE INDEX idx_ratings_tenant ON ratings(tenant_id);
CREATE INDEX idx_ratings_announcement ON ratings(announcement_id);
CREATE INDEX idx_ratings_rater ON ratings(rater_id);
CREATE INDEX idx_ratings_rated ON ratings(rated_id);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 8. EVENT_LOGS - Log eventi per audit e analytics
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE event_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    event_type VARCHAR(100) NOT NULL,          -- Tipo evento (user_signup, announcement_created, etc.)
    entity_type VARCHAR(50),                   -- Tipo entità (user, announcement, message, etc.)
    entity_id INTEGER,                         -- ID entità coinvolta
    metadata JSONB,                            -- Dati aggiuntivi in formato JSON
    ip_address INET,                           -- Indirizzo IP utente
    user_agent TEXT,                           -- User agent browser
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per performance e analytics
CREATE INDEX idx_event_logs_tenant ON event_logs(tenant_id);
CREATE INDEX idx_event_logs_user ON event_logs(user_id);
CREATE INDEX idx_event_logs_type ON event_logs(event_type);
CREATE INDEX idx_event_logs_entity ON event_logs(entity_type, entity_id);
CREATE INDEX idx_event_logs_created ON event_logs(created_at DESC);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 9. FUNZIONI E TRIGGER per aggiornamento automatico timestamp
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

-- Funzione per aggiornare updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger per aggiornamento automatico updated_at
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_announcements_updated_at BEFORE UPDATE ON announcements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 10. COMMENTI FINALI
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

-- Schema creato con successo!
-- Questo schema supporta:
--   ✅ Multi-tenancy completa
--   ✅ Gestione utenti con Supabase Auth
--   ✅ Annunci flessibili (progetti, lavori, servizi, materiali)
--   ✅ Sistema messaggistica
--   ✅ Valutazioni e feedback
--   ✅ Audit trail completo
--   ✅ Performance ottimizzate con indici strategici
