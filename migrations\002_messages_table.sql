-- 📄 migrations/002_messages_table.sql
-- Tabella per i messaggi di chat degli annunci con Supabase Realtime

-- Creazione tabella messages
CREATE TABLE IF NOT EXISTS messages (
    id SERIAL PRIMARY KEY,
    announcement_id INTEGER NOT NULL REFERENCES announcements(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indici per performance
CREATE INDEX IF NOT EXISTS idx_messages_announcement_id ON messages(announcement_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_user_id ON messages(user_id);

-- Abilita Row Level Security
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Policy per SELECT: tutti possono leggere i messaggi degli annunci del loro tenant
-- (assumendo che announcement_id sia collegato a tenant tramite announcements table)
CREATE POLICY "messages_select_policy" ON messages
    FOR SELECT
    USING (
        announcement_id IN (
            SELECT id FROM announcements 
            WHERE tenant_id = current_setting('app.current_tenant_id', true)::integer
        )
    );

-- Policy per INSERT: utenti autenticati possono inserire messaggi
CREATE POLICY "messages_insert_policy" ON messages
    FOR INSERT
    WITH CHECK (
        auth.uid() IS NOT NULL AND
        announcement_id IN (
            SELECT id FROM announcements 
            WHERE tenant_id = current_setting('app.current_tenant_id', true)::integer
        )
    );

-- Abilita realtime per la tabella messages
ALTER PUBLICATION supabase_realtime ADD TABLE messages;

-- Commenti per documentazione
COMMENT ON TABLE messages IS 'Messaggi di chat per gli annunci con supporto realtime';
COMMENT ON COLUMN messages.announcement_id IS 'ID dell''annuncio a cui appartiene il messaggio';
COMMENT ON COLUMN messages.user_id IS 'UUID dell''utente che ha inviato il messaggio';
COMMENT ON COLUMN messages.content IS 'Contenuto del messaggio';
COMMENT ON COLUMN messages.created_at IS 'Timestamp di creazione del messaggio';
