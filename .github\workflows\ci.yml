# ============================================================
# Workflow: CI/CD – lint, test, build e deploy su Vercel
# Corso   : DevOps per Web App – lezione "GitHub Actions"
# ------------------------------------------------------------
# Questo workflow parte ad ogni push su `main` (e su ogni PR)
# ed esegue:
#   1. Lint del codice (eslint / prettier)
#   2. Test (Jest o Vitest)
#   3. Build di produzione (Next.js)
#   4. Deploy automatico su Vercel (preview o prod)
#
# Requisiti (Secrets nel repo GitHub):
#   • VERCEL_TOKEN        – token personale Vercel
#   • VERCEL_ORG_ID       – ID dell'organizzazione
#   • VERCEL_PROJECT_ID   – ID del progetto Vercel
#   • DATABASE_URL        – connessione Postgres (RDS)
#   • SUPABASE_URL        – endpoint Supabase
#   • SUPABASE_ANON_KEY   – chiave anon Supabase
#   • RDS_INSTANCE_ID     – ID istanza RDS per monitoring
#   • ALERT_EMAIL         – email per notifiche CloudWatch (opzionale)
#   • AWS_ACCESS_KEY_ID   – credenziali AWS per CloudWatch
#   • AWS_SECRET_ACCESS_KEY – credenziali AWS per CloudWatch
#   • AWS_DEFAULT_REGION  – regione AWS (default: us-east-1)
# ------------------------------------------------------------

name: CI/CD Pipeline

# 🎯 Trigger: quando eseguire il workflow
on:
  push:
    branches: [main]              # Deploy produzione su push a main
  pull_request:                   # Test su ogni PR per validazione
    branches: [main]

# 🌍 Variabili d'ambiente globali per tutti i job
env:
  NODE_VERSION: "20"                           # Usa Node 20 LTS (stabile)
  NEXT_TELEMETRY_DISABLED: "1"                 # Disabilita telemetry Next.js per privacy
  DATABASE_URL: ${{ secrets.DATABASE_URL }}    # Connessione PostgreSQL (AWS RDS)
  SUPABASE_URL: ${{ secrets.SUPABASE_URL }}    # URL progetto Supabase
  SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}  # Chiave pubblica Supabase

# 🏗️ Jobs: sequenza di operazioni da eseguire
jobs:
  # Job principale: build, test e deploy
  build-test-deploy:
    name: 🚀 Build, Test & Deploy
    runs-on: ubuntu-latest                     # Usa runner Ubuntu (veloce e gratuito)
    
    # Evita esecuzioni multiple simultanee per lo stesso branch
    concurrency:
      group: "ci-${{ github.ref }}"
      cancel-in-progress: true
    
    steps:
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 1️⃣  SETUP: Preparazione ambiente di build
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      
      - name: ⬇️ Checkout repository
        uses: actions/checkout@v4
        with:
          # Scarica tutto lo storico per analisi complete
          fetch-depth: 0
      
      - name: 🛠️ Setup Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          # Usa cache npm per velocizzare installazioni successive
          cache: "npm"
          cache-dependency-path: "package-lock.json"
      
      - name: 📦 Installa dipendenze
        run: |
          echo "📋 Installazione dipendenze Node.js..."
          npm ci --prefer-offline --no-audit
          echo "✅ Dipendenze installate con successo"
      
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 2️⃣  QUALITY: Controllo qualità del codice
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      
      - name: 🔍 Lint del codice
        run: |
          echo "🔍 Esecuzione ESLint per controllo qualità codice..."
          npm run lint
          echo "✅ Lint completato senza errori"
      
      - name: 🧪 Esecuzione test
        run: |
          echo "🧪 Esecuzione test suite con Jest..."
          npm run test -- --ci --coverage --watchAll=false
          echo "✅ Tutti i test sono passati"
        env:
          # Variabili specifiche per i test
          NODE_ENV: test
      
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 3️⃣  BUILD: Compilazione per produzione
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

      - name: 🛠️ Build di produzione
        run: |
          echo "🛠️ Compilazione build di produzione Next.js..."
          npm run build
          echo "✅ Build completata con successo"
        env:
          # Variabili per build di produzione
          NODE_ENV: production

      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 4️⃣  E2E TESTS: Test end-to-end con Playwright
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

      - name: 🎭 Installa Playwright browsers
        run: |
          echo "🎭 Installazione browser Playwright per test E2E..."
          npx playwright install --with-deps chromium
          echo "✅ Browser Playwright installati"

      - name: 🧑‍💻 Esegui test E2E con Playwright
        run: |
          echo "🧑‍💻 Avvio server Next.js per test E2E..."
          npm run start &
          SERVER_PID=$!

          echo "⏳ Attesa che il server sia pronto..."
          npx wait-on http://localhost:3000 --timeout 60000

          echo "🎭 Esecuzione test E2E..."
          npm run e2e

          echo "🛑 Terminazione server..."
          kill $SERVER_PID || true
          echo "✅ Test E2E completati"
        env:
          # Variabili per test E2E
          E2E_BASE_URL: "http://localhost:3000"
          NODE_ENV: test

      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 5️⃣  MIGRATIONS: Automigrazioni database su RDS
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

      - name: 🗄️ Installa PostgreSQL client
        run: |
          echo "📦 Installazione PostgreSQL client per migrazioni..."
          sudo apt-get update -y
          sudo apt-get install -y postgresql-client
          echo "✅ PostgreSQL client installato"

      - name: 🗄️ Esegui automigrazioni database
        run: |
          echo "🗄️ Esecuzione automigrazioni su database RDS..."
          ./scripts/run_migrations.sh
          echo "✅ Migrazioni completate"
        env:
          # DATABASE_URL viene passata dai secrets GitHub
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
      
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 6️⃣  DEPLOY: Pubblicazione su Vercel
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

      - name: 🚀 Deploy su Vercel
        uses: amondnet/vercel-action@v25
        id: vercel-deploy
        with:
          # Credenziali Vercel (configurate come GitHub Secrets)
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          
          # Logica di deploy:
          # - Push su main → deploy produzione (--prod)
          # - PR o altri branch → deploy preview
          vercel-args: ${{ github.ref == 'refs/heads/main' && '--prod' || '' }}
          
          # Directory di lavoro (root del progetto)
          working-directory: ./
          
          # Passa le variabili d'ambiente a Vercel
          github-token: ${{ secrets.GITHUB_TOKEN }}
      
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 7️⃣  MONITORING: Setup CloudWatch alarm per RDS
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

      - name: 🛎️ Setup RDS CloudWatch alarm
        if: github.ref == 'refs/heads/main'
        run: |
          echo "🛎️ Configurazione allarme CloudWatch per RDS..."
          sudo apt-get update -y
          sudo apt-get install -y awscli
          ./scripts/rds_monitor.sh
          echo "✅ Allarme RDS configurato con successo"
        env:
          # Configurazione allarme RDS (aggiungi questi secrets nel repo GitHub)
          RDS_INSTANCE_ID: ${{ secrets.RDS_INSTANCE_ID }}     # ID istanza RDS
          ALERT_EMAIL: ${{ secrets.ALERT_EMAIL }}             # Email per notifiche (opzionale)
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }} # Credenziali AWS
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION || 'us-east-1' }}

      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      # 8️⃣  NOTIFICATION: Notifica risultato deploy
      # ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      
      - name: 📢 Notifica successo deploy
        if: success()
        run: |
          echo "🎉 Deploy completato con successo!"
          echo "🌐 URL: ${{ steps.vercel-deploy.outputs.preview-url }}"
          echo "📊 Tipo deploy: ${{ github.ref == 'refs/heads/main' && 'PRODUZIONE' || 'PREVIEW' }}"
          echo "🔗 Commit: ${{ github.sha }}"
      
      - name: ❌ Notifica fallimento
        if: failure()
        run: |
          echo "❌ Deploy fallito!"
          echo "🔍 Controlla i log sopra per dettagli sull'errore"
          echo "💡 Possibili cause:"
          echo "   • Errori di lint o test"
          echo "   • Problemi di build Next.js"
          echo "   • Configurazione Vercel incorretta"
          echo "   • Variabili d'ambiente mancanti"
