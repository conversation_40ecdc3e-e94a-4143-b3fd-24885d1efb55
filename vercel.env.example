# =============================================================
# File: vercel.env.example
# Scopo: Template variabili d'ambiente per deployment Vercel
# Corso: DevOps per Web App – lezione "Deployment & Hosting"
# -------------------------------------------------------------
# Questo file serve come template per configurare le variabili
# d'ambiente necessarie per il deployment su Vercel.
#
# 📋 Come usare:
#   1. Copia questo file: cp vercel.env.example vercel.env.local
#   2. Sostituisci i valori di esempio con quelli reali
#   3. Importa le variabili nella dashboard Vercel
#
# ⚠️  IMPORTANTE: Non committare mai file .env con valori reali!
# =============================================================

# -------------------------------------------------------------
# 🗄️  DATABASE - Connessione PostgreSQL (AWS RDS)
# -------------------------------------------------------------
# Ottieni questo valore dopo aver eseguito ./scripts/create_rds.sh
# Formato: postgres://username:password@endpoint:port/database
DATABASE_URL="postgres://rebuildlink_user:<EMAIL>:5432/rebuildlink"

# -------------------------------------------------------------
# 🔐 SUPABASE - Autenticazione e Storage
# -------------------------------------------------------------
# Ottieni questi valori dalla dashboard Supabase:
# Dashboard > Settings > API

# URL del progetto Supabase (pubblico, sicuro da esporre)
SUPABASE_URL="https://your-project-id.supabase.co"

# Chiave anonima pubblica (per client-side, sicura da esporre)
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlvdXItcHJvamVjdC1pZCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjc4ODg4ODg4LCJleHAiOjE5OTQ0NjQ4ODh9.your-anon-key-signature"

# Chiave service role (per server-side, MANTIENI SEGRETA!)
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlvdXItcHJvamVjdC1pZCIsInJvbGUiOiJzZXJ2aWNlX3JvbGUiLCJpYXQiOjE2Nzg4ODg4ODgsImV4cCI6MTk5NDQ2NDg4OH0.your-service-role-key-signature"

# -------------------------------------------------------------
# 🌐 APPLICAZIONE - Configurazione generale
# -------------------------------------------------------------
# URL base dell'applicazione in produzione (Vercel lo imposta automaticamente)
NEXT_PUBLIC_APP_URL="https://your-app-name.vercel.app"

# Ambiente di esecuzione (Vercel lo imposta automaticamente)
NODE_ENV="production"

# -------------------------------------------------------------
# 📊 OPZIONALI - Analytics e Monitoring
# -------------------------------------------------------------
# Decommentare se implementi analytics o monitoring

# Google Analytics (se implementato)
# NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"

# Sentry per error tracking (se implementato)
# SENTRY_DSN="https://<EMAIL>/project-id"

# Vercel Analytics (automatico se abilitato nel progetto)
# NEXT_PUBLIC_VERCEL_ANALYTICS_ID="your-analytics-id"

# -------------------------------------------------------------
# 🔧 SVILUPPO - Variabili per debugging (solo se necessario)
# -------------------------------------------------------------
# Decommentare solo per debugging in produzione

# Abilita logging dettagliato
# DEBUG="true"

# Livello di log (error, warn, info, debug)
# LOG_LEVEL="info"

# -------------------------------------------------------------
# 📝 NOTE PER IL DEPLOYMENT
# -------------------------------------------------------------
# 
# 1. VERCEL DASHBOARD:
#    • Vai su vercel.com > Il tuo progetto > Settings > Environment Variables
#    • Aggiungi ogni variabile manualmente o importa da file
#    • Assicurati di selezionare gli ambienti corretti (Production, Preview, Development)
#
# 2. VERCEL CLI:
#    • Installa: npm i -g vercel
#    • Login: vercel login
#    • Deploy: vercel --prod
#    • Le variabili vengono lette automaticamente da .env.local
#
# 3. SICUREZZA:
#    • DATABASE_URL: Contiene password, mantieni segreta
#    • SUPABASE_ANON_KEY: Sicura da esporre (prefisso NEXT_PUBLIC_)
#    • SUPABASE_SERVICE_ROLE_KEY: MANTIENI SEGRETA, solo server-side
#
# 4. SINCRONIZZAZIONE:
#    • Mantieni allineate le variabili tra GitHub Secrets e Vercel
#    • Usa lo stesso DATABASE_URL per entrambi gli ambienti
#    • Testa sempre dopo aver aggiornato le variabili
#
# 5. TROUBLESHOOTING:
#    • Se l'app non si connette al DB: verifica DATABASE_URL
#    • Se l'auth non funziona: verifica SUPABASE_URL e SUPABASE_ANON_KEY
#    • Se le API server falliscono: verifica SUPABASE_SERVICE_ROLE_KEY
#
# -------------------------------------------------------------
