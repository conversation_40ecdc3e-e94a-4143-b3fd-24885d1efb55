# 🏗️ ReBuild Link - Terraform Infrastructure

Questo directory contiene l'Infrastructure as Code (IaC) per il progetto ReBuild Link, gestendo automaticamente il provisioning di AWS RDS, GitHub secrets e Vercel environment variables.

## 📁 Struttura File

```
infra/terraform/
├── provider.tf              # Configurazione provider (AWS, GitHub, Vercel)
├── variables.tf             # Definizione variabili con validazione
├── network.tf               # VPC, Security Groups, Subnet Groups
├── db.tf                    # RDS PostgreSQL con monitoring
├── secrets.tf               # GitHub Actions secrets + Vercel env vars
├── outputs.tf               # Output informativi post-deployment
├── terraform.tfvars.example # Template configurazione
├── .gitignore               # File da escludere dal git
└── README.md                # Questa documentazione
```

## 🚀 Quick Start

### 1. Prerequisiti

- [Terraform](https://terraform.io/downloads) >= 1.6.0
- [AWS CLI](https://aws.amazon.com/cli/) configurato
- Token GitHub con scope `repo`
- Token Vercel con accesso al progetto

### 2. Configurazione

```bash
# Copia il template di configurazione
cp terraform.tfvars.example terraform.tfvars

# Modifica terraform.tfvars con i tuoi valori reali
# IMPORTANTE: Non committare mai questo file!
```

### 3. Deployment

```bash
# Inizializza Terraform (prima volta)
terraform init

# Pianifica le modifiche
terraform plan

# Applica l'infrastruttura
terraform apply
```

## 🔧 Configurazione Variabili

### Variabili Obbligatorie

| Variabile | Descrizione | Esempio |
|-----------|-------------|---------|
| `db_password` | Password database PostgreSQL | `"SuperSegreta123!"` |
| `allowed_cidr` | IP pubblico per accesso DB | `"*******/32"` |
| `github_token` | Token GitHub PAT | `"ghp_xxx..."` |
| `github_owner` | Username/org GitHub | `"tuo-username"` |
| `github_repo` | Nome repository | `"rebuild-link"` |
| `supabase_url` | URL progetto Supabase | `"https://xxx.supabase.co"` |
| `supabase_anon_key` | Chiave anonima Supabase | `"eyJhbGciOi..."` |
| `vercel_token` | Token Vercel | `"vercel_pat_xxx..."` |
| `vercel_project_id` | ID progetto Vercel | `"prj_xxx..."` |

### Come Ottenere i Valori

#### IP Pubblico
```bash
curl ifconfig.me
# Aggiungi /32 alla fine: *******/32
```

#### GitHub Token
1. Vai su [GitHub Settings > Tokens](https://github.com/settings/tokens)
2. Crea nuovo token con scope `repo`
3. Copia il token generato

#### Supabase Credentials
1. Vai su [Supabase Dashboard](https://app.supabase.com)
2. Seleziona il progetto
3. Settings > API
4. Copia URL e anon key

#### Vercel Token e Project ID
1. Token: [Vercel Account > Tokens](https://vercel.com/account/tokens)
2. Project ID: Vercel Project > Settings > General

## 📊 Risorse Create

### AWS Resources
- **RDS PostgreSQL 15**: Istanza db.t3.micro con 20GB storage
- **Security Group**: Accesso limitato al tuo IP sulla porta 5432
- **DB Subnet Group**: Configurazione subnet per RDS
- **IAM Role**: Per enhanced monitoring RDS
- **CloudWatch Alarms**: CPU e connessioni database

### GitHub Integration
- **DATABASE_URL**: Stringa connessione database
- **SUPABASE_URL**: URL progetto Supabase
- **SUPABASE_ANON_KEY**: Chiave anonima Supabase
- **RDS_INSTANCE_ID**: ID istanza RDS
- **AWS_DEFAULT_REGION**: Regione AWS

### Vercel Integration
- **DATABASE_URL**: Per server-side (encrypted)
- **NEXT_PUBLIC_SUPABASE_URL**: Per client-side (plain)
- **SUPABASE_URL**: Per server-side (encrypted)
- **NEXT_PUBLIC_SUPABASE_ANON_KEY**: Per client-side (plain)
- **SUPABASE_ANON_KEY**: Per server-side (encrypted)
- **NODE_ENV**: Environment specifico
- **NEXT_TELEMETRY_DISABLED**: Privacy setting

## 🔍 Comandi Utili

### Gestione State
```bash
# Visualizza stato attuale
terraform show

# Lista risorse gestite
terraform state list

# Refresh stato (sincronizza con AWS)
terraform refresh
```

### Debugging
```bash
# Validazione configurazione
terraform validate

# Formattazione codice
terraform fmt

# Log dettagliati
export TF_LOG=DEBUG
terraform apply
```

### Import Risorse Esistenti
```bash
# Import istanza RDS esistente
terraform import aws_db_instance.postgres rebuildlink-db

# Import security group esistente
terraform import aws_security_group.rds_sg sg-xxxxxxxxx
```

## 🛡️ Sicurezza

### File Sensibili
- `terraform.tfvars` - **MAI COMMITTARE**
- `terraform.tfstate` - **MAI COMMITTARE**
- `*.backup` - **MAI COMMITTARE**

### Best Practices
- ✅ Usa password complesse (>8 caratteri)
- ✅ Limita `allowed_cidr` al tuo IP specifico
- ✅ Ruota periodicamente i token
- ✅ Abilita 2FA su tutti gli account
- ✅ Monitora i log CloudWatch

## 🔄 Lifecycle Management

### Aggiornamenti
```bash
# Modifica terraform.tfvars o file .tf
terraform plan    # Anteprima modifiche
terraform apply   # Applica modifiche
```

### Rollback
```bash
# Torna a versione precedente del codice
git checkout HEAD~1 infra/terraform/
terraform plan
terraform apply
```

### Distruzione
```bash
# ⚠️ ATTENZIONE: Distrugge TUTTA l'infrastruttura
terraform destroy
```

## 📈 Monitoring

### CloudWatch Alarms
- **CPU Utilization**: Allarme se > 80% per 2 periodi
- **Database Connections**: Allarme se > 15 connessioni

### Performance Insights
- Abilitato automaticamente
- Analisi query e performance
- Retention 7 giorni (free tier)

### Enhanced Monitoring
- Metriche ogni 60 secondi
- Monitoring OS-level
- IAM role automatico

## 🆘 Troubleshooting

### Errori Comuni

**Provider not found:**
```bash
terraform init
```

**State lock:**
```bash
# Se il processo si blocca
terraform force-unlock LOCK_ID
```

**Resource already exists:**
```bash
# Import la risorsa esistente
terraform import RESOURCE_TYPE.NAME RESOURCE_ID
```

**Permission denied:**
```bash
# Verifica credenziali AWS
aws sts get-caller-identity
```

### Log e Debug
```bash
# Abilita log dettagliati
export TF_LOG=DEBUG
export TF_LOG_PATH=terraform.log
terraform apply
```

## 📞 Supporto

Per problemi o domande:
1. Controlla i log: `terraform.log`
2. Verifica la documentazione: [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
3. Controlla lo stato: `terraform show`

---

💡 **Tip**: Mantieni sempre un backup del file `terraform.tfstate` prima di modifiche importanti!
