import { ReactNode } from 'react';

interface KpiCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
}

/**
 * Card per visualizzare metriche KPI
 * Design pulito con icona opzionale e valori prominenti
 */
export default function KpiCard({ title, value, icon }: KpiCardProps) {
  return (
    <div className="rounded-xl bg-white shadow p-6 flex items-center gap-4">
      {icon && <span className="text-primary-500">{icon}</span>}
      <div>
        <p className="text-sm text-neutral-500">{title}</p>
        <p className="text-2xl font-heading font-semibold">{value}</p>
      </div>
    </div>
  );
}
