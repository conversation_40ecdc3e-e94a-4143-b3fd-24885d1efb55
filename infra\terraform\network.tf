# =============================================================================
# Network Configuration per ReBuild Link Infrastructure
# =============================================================================
# Questo file gestisce la configurazione di rete per l'infrastruttura:
# - VPC di default AWS
# - Security Group per RDS PostgreSQL
# - Regole di ingresso/uscita per database
# =============================================================================

# =============================================================================
# VPC Data Source
# =============================================================================
# Utilizza la VPC di default di AWS per semplicità
# In produzione enterprise, si potrebbe creare una VPC dedicata
data "aws_vpc" "default" {
  default = true
  
  tags = {
    Name = "Default VPC"
  }
}

# Ottieni informazioni sulle subnet disponibili nella VPC di default
data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
  
  filter {
    name   = "default-for-az"
    values = ["true"]
  }
}

# =============================================================================
# Security Group per RDS PostgreSQL
# =============================================================================
resource "aws_security_group" "rds_sg" {
  name_prefix = "${var.db_instance_id}-sg-"
  description = "Security Group per RDS PostgreSQL - ReBuild Link"
  vpc_id      = data.aws_vpc.default.id

  # Regola di ingresso: PostgreSQL dalla CIDR specificata
  ingress {
    description = "PostgreSQL access from allowed CIDR"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = [var.allowed_cidr]
  }

  # Regola di uscita: tutto il traffico (necessario per aggiornamenti, etc.)
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Tags per identificazione e gestione
  tags = {
    Name        = "${var.db_instance_id}-security-group"
    Purpose     = "RDS PostgreSQL Access"
    Database    = var.db_instance_id
    AllowedCIDR = var.allowed_cidr
  }

  # Lifecycle rule per evitare problemi durante aggiornamenti
  lifecycle {
    create_before_destroy = true
  }
}

# =============================================================================
# DB Subnet Group (opzionale, per controllo maggiore)
# =============================================================================
# Crea un subnet group personalizzato per RDS
# Questo permette maggiore controllo su dove viene posizionato il database
resource "aws_db_subnet_group" "rds_subnet_group" {
  name       = "${var.db_instance_id}-subnet-group"
  subnet_ids = data.aws_subnets.default.ids

  tags = {
    Name    = "${var.db_instance_id} DB Subnet Group"
    Purpose = "RDS PostgreSQL Subnet Group"
  }
}

# =============================================================================
# Outputs per debugging e riferimenti
# =============================================================================
output "vpc_id" {
  description = "ID della VPC utilizzata"
  value       = data.aws_vpc.default.id
}

output "security_group_id" {
  description = "ID del Security Group creato per RDS"
  value       = aws_security_group.rds_sg.id
}

output "security_group_name" {
  description = "Nome del Security Group creato per RDS"
  value       = aws_security_group.rds_sg.name
}

output "allowed_cidr_block" {
  description = "CIDR block autorizzato per l'accesso al database"
  value       = var.allowed_cidr
}

output "subnet_group_name" {
  description = "Nome del DB Subnet Group"
  value       = aws_db_subnet_group.rds_subnet_group.name
}
