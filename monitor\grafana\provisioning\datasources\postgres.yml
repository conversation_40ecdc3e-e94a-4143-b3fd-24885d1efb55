# =============================================================================
# Grafana Datasource Configuration per PostgreSQL RDS
# =============================================================================
# Configurazione automatica del datasource PostgreSQL per Grafana.
# Questo file viene caricato automaticamente all'avvio di Grafana.
# 
# IMPORTANTE: Modifica i valori di connessione con quelli reali del tuo RDS!
# =============================================================================

apiVersion: 1

# =============================================================================
# Datasources Configuration
# =============================================================================
datasources:
  # ---------------------------------------------------------------------------
  # PostgreSQL RDS - Datasource principale
  # ---------------------------------------------------------------------------
  - name: PostgreSQL
    type: postgres
    access: proxy
    
    # URL di connessione al database RDS
    # IMPORTANTE: Sostituisci con i tuoi valori reali!
    url: your-rds-endpoint.region.rds.amazonaws.com:5432
    database: rebuildlink
    user: rebuild_user
    
    # Password (usa variabile d'ambiente per sicurezza)
    secureJsonData:
      password: ${POSTGRES_PASSWORD}
    
    # Configurazione JSON
    jsonData:
      # SSL Configuration per RDS
      sslmode: require
      
      # PostgreSQL version
      postgresVersion: 1500  # PostgreSQL 15
      
      # Timezone
      timescaledb: false
      
      # Connection limits
      maxOpenConns: 5
      maxIdleConns: 2
      connMaxLifetime: 14400  # 4 hours
      
      # Query timeout
      queryTimeout: 60s
    
    # Configurazione generale
    isDefault: true
    editable: true
    
    # Organizzazione
    orgId: 1
    
    # Versione
    version: 1

  # ---------------------------------------------------------------------------
  # PostgreSQL Read Replica (opzionale)
  # ---------------------------------------------------------------------------
  - name: PostgreSQL-ReadOnly
    type: postgres
    access: proxy
    
    # URL read replica (se disponibile)
    url: your-rds-readonly-endpoint.region.rds.amazonaws.com:5432
    database: rebuildlink
    user: rebuild_user
    
    # Password
    secureJsonData:
      password: ${POSTGRES_PASSWORD}
    
    # Configurazione JSON
    jsonData:
      sslmode: require
      postgresVersion: 1500
      timescaledb: false
      maxOpenConns: 3
      maxIdleConns: 1
      connMaxLifetime: 14400
      queryTimeout: 60s
    
    # Non è il datasource di default
    isDefault: false
    editable: true
    orgId: 1
    version: 1

# =============================================================================
# Istruzioni per configurazione
# =============================================================================
# 
# 1. Modifica i valori di connessione:
#    - url: Endpoint del tuo RDS PostgreSQL
#    - database: Nome del database (default: rebuildlink)
#    - user: Username del database (default: rebuild_user)
# 
# 2. Configura la password tramite variabile d'ambiente:
#    export POSTGRES_PASSWORD="your-database-password"
#    
#    Oppure modifica docker-compose.yml:
#    environment:
#      - POSTGRES_PASSWORD=your-database-password
# 
# 3. Per RDS, assicurati che:
#    - Il Security Group permetta connessioni dalla tua IP sulla porta 5432
#    - Il database sia publicly accessible (se Grafana è esterno)
#    - SSL sia configurato correttamente (sslmode: require)
# 
# 4. Test della connessione:
#    - Avvia Grafana: docker compose up -d
#    - Vai su http://localhost:3001
#    - Login: admin/admin
#    - Vai su Configuration > Data Sources
#    - Clicca su PostgreSQL
#    - Clicca "Save & Test"
# 
# =============================================================================
