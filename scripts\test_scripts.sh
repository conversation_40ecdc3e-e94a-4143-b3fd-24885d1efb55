#!/usr/bin/env bash
# ============================================================
# Script: test_scripts.sh
# Scopo : Test di validazione per gli script RDS
# Autore: ReBuild Link Team
# ------------------------------------------------------------
# Questo script testa la sintassi e le dipendenze degli
# script RDS senza effettivamente creare risorse AWS.
#
# ============================================================

set -euo pipefail

echo "🧪 Test degli script RDS..."
echo ""

# -------------------------------
# 1. Test sintassi Bash
# -------------------------------
echo "1️⃣  Test sintassi Bash..."

scripts=("create_rds.sh" "check_rds_status.sh" "delete_rds.sh" "set_github_secrets.sh" "check_env_vars.sh" "setup_vercel_secrets.sh" "run_migrations.sh" "bootstrap_cli_project.sh")

for script in "${scripts[@]}"; do
    if bash -n "scripts/$script"; then
        echo "   ✅ $script - sintassi OK"
    else
        echo "   ❌ $script - errore di sintassi"
        exit 1
    fi
done

echo ""

# -------------------------------
# 2. Test dipendenze
# -------------------------------
echo "2️⃣  Test dipendenze..."

dependencies=("curl" "aws" "jq" "gh" "vercel" "psql")
missing_deps=()

for dep in "${dependencies[@]}"; do
    if command -v "$dep" >/dev/null 2>&1; then
        echo "   ✅ $dep - disponibile"
    else
        echo "   ⚠️  $dep - non trovato"
        missing_deps+=("$dep")
    fi
done

if [ ${#missing_deps[@]} -gt 0 ]; then
    echo ""
    echo "❌ Dipendenze mancanti: ${missing_deps[*]}"
    echo ""
    echo "📦 Installazione suggerita:"
    for dep in "${missing_deps[@]}"; do
        case "$dep" in
            "curl")
                echo "   sudo apt install curl  # Ubuntu/Debian"
                echo "   brew install curl      # macOS"
                ;;
            "aws")
                echo "   pip install awscli     # Python"
                echo "   brew install awscli    # macOS"
                ;;
            "jq")
                echo "   sudo apt install jq    # Ubuntu/Debian"
                echo "   brew install jq        # macOS"
                ;;
            "gh")
                echo "   sudo apt install gh    # Ubuntu/Debian"
                echo "   brew install gh        # macOS"
                echo "   winget install GitHub.cli # Windows"
                ;;
            "vercel")
                echo "   npm i -g vercel        # Global install"
                echo "   yarn global add vercel # Yarn alternative"
                ;;
            "psql")
                echo "   sudo apt install postgresql-client # Ubuntu/Debian"
                echo "   brew install postgresql # macOS"
                echo "   # Windows: Download from postgresql.org"
                ;;
        esac
    done
    echo ""
    echo "⚠️  Gli script potrebbero non funzionare senza queste dipendenze."
else
    echo ""
    echo "✅ Tutte le dipendenze sono disponibili!"
fi

echo ""

# -------------------------------
# 3. Test configurazione AWS (opzionale)
# -------------------------------
echo "3️⃣  Test configurazione AWS..."

if command -v aws >/dev/null 2>&1; then
    if aws sts get-caller-identity >/dev/null 2>&1; then
        echo "   ✅ AWS CLI configurata correttamente"
        
        # Mostra informazioni account
        account_id=$(aws sts get-caller-identity --query Account --output text 2>/dev/null || echo "N/A")
        region=$(aws configure get region 2>/dev/null || echo "N/A")
        echo "   📋 Account ID: $account_id"
        echo "   🌍 Regione default: $region"
    else
        echo "   ⚠️  AWS CLI non configurata"
        echo "   💡 Esegui: aws configure"
    fi
else
    echo "   ⚠️  AWS CLI non installata"
fi

# Test GitHub CLI
if command -v gh >/dev/null 2>&1; then
    if gh auth status >/dev/null 2>&1; then
        echo "   ✅ GitHub CLI configurata correttamente"

        # Mostra informazioni utente
        username=$(gh api user --jq .login 2>/dev/null || echo "N/A")
        echo "   👤 Utente GitHub: $username"
    else
        echo "   ⚠️  GitHub CLI non autenticata"
        echo "   💡 Esegui: gh auth login"
    fi
else
    echo "   ⚠️  GitHub CLI non installata"
fi

# Test Vercel CLI
if command -v vercel >/dev/null 2>&1; then
    if vercel whoami >/dev/null 2>&1; then
        echo "   ✅ Vercel CLI configurata correttamente"

        # Mostra informazioni utente
        username=$(vercel whoami 2>/dev/null || echo "N/A")
        echo "   🚀 Utente Vercel: $username"
    else
        echo "   ⚠️  Vercel CLI non autenticata"
        echo "   💡 Esegui: vercel login"
    fi
else
    echo "   ⚠️  Vercel CLI non installata"
fi

echo ""

# -------------------------------
# 4. Test variabili d'ambiente
# -------------------------------
echo "4️⃣  Test variabili d'ambiente..."

if [[ -n "${DB_PASSWORD:-}" ]]; then
    echo "   ✅ DB_PASSWORD impostata"
else
    echo "   ⚠️  DB_PASSWORD non impostata"
    echo "   💡 Esegui: export DB_PASSWORD='TuaPasswordSicura123!'"
fi

echo ""

# -------------------------------
# 5. Riepilogo
# -------------------------------
echo "📊 Riepilogo test:"
echo ""

if [ ${#missing_deps[@]} -eq 0 ]; then
    echo "✅ Sintassi script: OK"
    echo "✅ Dipendenze: OK"
    
    aws_ok=false
    gh_ok=false
    vercel_ok=false

    if command -v aws >/dev/null 2>&1 && aws sts get-caller-identity >/dev/null 2>&1; then
        echo "✅ AWS CLI: OK"
        aws_ok=true
    else
        echo "⚠️  AWS CLI: Non configurata"
    fi

    if command -v gh >/dev/null 2>&1 && gh auth status >/dev/null 2>&1; then
        echo "✅ GitHub CLI: OK"
        gh_ok=true
    else
        echo "⚠️  GitHub CLI: Non configurata"
    fi

    if command -v vercel >/dev/null 2>&1 && vercel whoami >/dev/null 2>&1; then
        echo "✅ Vercel CLI: OK"
        vercel_ok=true
    else
        echo "⚠️  Vercel CLI: Non configurata"
    fi

    echo ""

    if $aws_ok && $gh_ok && $vercel_ok; then
        echo "🎉 Tutti i test superati! Gli script sono pronti per l'uso."
        echo ""
        echo "🚀 Workflow completo DevOps:"
        echo ""
        echo "   📋 Manuale (step-by-step):"
        echo "   1. export DB_PASSWORD='TuaPasswordSicura123!'"
        echo "   2. ./scripts/create_rds.sh"
        echo "   3. export DATABASE_URL='postgres://...'"
        echo "   4. ./scripts/run_migrations.sh  # Setup schema iniziale"
        echo "   5. export SUPABASE_URL='https://...'"
        echo "   6. export SUPABASE_ANON_KEY='eyJ...'"
        echo "   7. ./scripts/set_github_secrets.sh"
        echo "   8. ./scripts/setup_vercel_secrets.sh"
        echo "   9. git push → CI/CD automatico!"
        echo ""
        echo "   ⛳ Automatico (bootstrap completo):"
        echo "   1. export DATABASE_URL='postgres://...'"
        echo "   2. export SUPABASE_URL='https://...'"
        echo "   3. export SUPABASE_ANON_KEY='eyJ...'"
        echo "   4. ./scripts/bootstrap_cli_project.sh"
        echo "   5. git push → Deploy automatico!"
    else
        echo "⚡ Configurazioni mancanti:"
        if ! $aws_ok; then
            echo "   • AWS CLI: aws configure"
        fi
        if ! $gh_ok; then
            echo "   • GitHub CLI: gh auth login"
        fi
        if ! $vercel_ok; then
            echo "   • Vercel CLI: vercel login"
        fi
    fi
else
    echo "❌ Dipendenze: Mancanti (${missing_deps[*]})"
    echo ""
    echo "🔧 Installa le dipendenze mancanti prima di usare gli script."
fi

echo ""
echo "📚 Per maggiori informazioni: cat scripts/README.md"
