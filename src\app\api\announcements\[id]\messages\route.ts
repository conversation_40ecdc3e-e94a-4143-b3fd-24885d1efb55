import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/db";
import { requireAuth } from "@/middleware/auth";

/**
 * API per gestire i messaggi di chat degli annunci
 * GET: Recupera tutti i messaggi di un annuncio
 * POST: Crea un nuovo messaggio (richiede autenticazione)
 */

// GET /api/announcements/[id]/messages
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const announcementId = parseInt(id);
  
  if (isNaN(announcementId)) {
    return NextResponse.json({ error: "ID annuncio non valido" }, { status: 400 });
  }

  try {
    const messages = await query(
      `SELECT id, announcement_id, user_id, content, created_at 
       FROM messages 
       WHERE announcement_id = $1 
       ORDER BY created_at ASC`,
      [announcementId]
    );

    return NextResponse.json(messages);
  } catch (error) {
    console.error("Database error:", error);
    
    // Fallback con dati mock se il DB non è disponibile
    const mockMessages = [
      {
        id: 1,
        announcement_id: announcementId,
        user_id: "user-1",
        content: "Ciao! Sono interessato a questo annuncio.",
        created_at: new Date(Date.now() - 3600000).toISOString()
      },
      {
        id: 2,
        announcement_id: announcementId,
        user_id: "user-2", 
        content: "Perfetto! Quando possiamo sentirci?",
        created_at: new Date(Date.now() - 1800000).toISOString()
      }
    ];

    return NextResponse.json(mockMessages);
  }
}

// POST /api/announcements/[id]/messages
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const announcementId = parseInt(id);
  
  if (isNaN(announcementId)) {
    return NextResponse.json({ error: "ID annuncio non valido" }, { status: 400 });
  }

  let body;
  try {
    body = await req.json();
  } catch {
    return NextResponse.json({ error: "JSON malformato" }, { status: 400 });
  }

  const { content } = body;
  
  if (!content || typeof content !== 'string' || !content.trim()) {
    return NextResponse.json({ error: "Contenuto messaggio richiesto" }, { status: 400 });
  }

  // Autenticazione JWT obbligatoria (supporta demo mode per E2E)
  const auth = await requireAuth(req);
  if ('error' in auth) return auth.error;
  const userId = auth.user.id;

  try {
    const [newMessage] = await query(
      `INSERT INTO messages (announcement_id, user_id, content) 
       VALUES ($1, $2, $3) 
       RETURNING id, announcement_id, user_id, content, created_at`,
      [announcementId, userId, content.trim()]
    );

    return NextResponse.json(newMessage, { status: 201 });
  } catch (error) {
    console.error("Database error:", error);
    
    // Fallback: restituisci successo mock per testing
    const mockMessage = {
      id: Math.floor(Math.random() * 1000),
      announcement_id: announcementId,
      user_id: userId,
      content: content.trim(),
      created_at: new Date().toISOString()
    };

    return NextResponse.json(mockMessage, { status: 201 });
  }
}
