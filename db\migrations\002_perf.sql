-- =============================================================================
-- Migration: 002_perf.sql
-- Scopo: Performance optimization e monitoring per ReBuild Link
-- =============================================================================
-- Questa migrazione abilita il monitoring delle query e aggiunge indici
-- strategici per migliorare le performance dell'applicazione.
-- 
-- IMPORTANTE: Su AWS RDS, assicurati che il Parameter Group contenga
-- 'pg_stat_statements' in 'shared_preload_libraries' prima di eseguire.
-- =============================================================================

-- =============================================================================
-- 1. Estensione pg_stat_statements per query profiling
-- =============================================================================
-- Abilita il tracking delle statistiche delle query per analisi performance
-- Permette di identificare query lente, frequenti e problematiche

CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Verifica che l'estensione sia stata creata correttamente
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
    ) THEN
        RAISE NOTICE 'ATTENZIONE: pg_stat_statements non è stata abilitata. Verifica shared_preload_libraries in postgresql.conf';
    ELSE
        RAISE NOTICE 'SUCCESS: pg_stat_statements abilitata correttamente';
    END IF;
END $$;

-- =============================================================================
-- 2. Indici per performance tenants
-- =============================================================================
-- Ottimizza lookup per subdomain (routing multi-tenant)

CREATE INDEX IF NOT EXISTS idx_tenants_subdomain
ON tenants (subdomain);

-- Indice per ricerca tenants per slug (se presente)
CREATE INDEX IF NOT EXISTS idx_tenants_slug
ON tenants (slug) WHERE slug IS NOT NULL;

-- =============================================================================
-- 3. Indici per performance announcements
-- =============================================================================
-- Ottimizza query principali per gli annunci

-- Indice composito per listing annunci per tenant (query più comune)
CREATE INDEX IF NOT EXISTS idx_announcements_tenant_created
ON announcements (tenant_id, created_at DESC);

-- Indice per ricerca annunci per categoria
CREATE INDEX IF NOT EXISTS idx_announcements_tenant_category
ON announcements (tenant_id, category_id) WHERE category_id IS NOT NULL;

-- Indice per ricerca full-text su titolo e descrizione
CREATE INDEX IF NOT EXISTS idx_announcements_search
ON announcements USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Indice per annunci attivi (non scaduti)
CREATE INDEX IF NOT EXISTS idx_announcements_active
ON announcements (tenant_id, created_at DESC) 
WHERE expires_at IS NULL OR expires_at > NOW();

-- =============================================================================
-- 4. Indici per performance messages
-- =============================================================================
-- Ottimizza sistema di messaggistica

-- Indice per inbox utente (messaggi ricevuti)
CREATE INDEX IF NOT EXISTS idx_messages_receiver_created
ON messages (receiver_user_id, created_at DESC);

-- Indice per outbox utente (messaggi inviati)
CREATE INDEX IF NOT EXISTS idx_messages_sender_created
ON messages (sender_user_id, created_at DESC);

-- Indice per messaggi non letti
CREATE INDEX IF NOT EXISTS idx_messages_unread
ON messages (receiver_user_id, created_at DESC) WHERE read_at IS NULL;

-- =============================================================================
-- 5. Indici per performance users
-- =============================================================================
-- Ottimizza autenticazione e lookup utenti

-- Indice per login per email (se non già presente)
CREATE INDEX IF NOT EXISTS idx_users_email
ON users (email) WHERE email IS NOT NULL;

-- Indice per utenti per tenant
CREATE INDEX IF NOT EXISTS idx_users_tenant
ON users (tenant_id);

-- =============================================================================
-- 6. Indici per performance ratings/reviews
-- =============================================================================
-- Ottimizza sistema di valutazioni (se presente)

-- Indice per rating per annuncio
CREATE INDEX IF NOT EXISTS idx_ratings_announcement
ON ratings (announcement_id, created_at DESC) WHERE announcement_id IS NOT NULL;

-- Indice per rating per utente
CREATE INDEX IF NOT EXISTS idx_ratings_user
ON ratings (rated_user_id, created_at DESC) WHERE rated_user_id IS NOT NULL;

-- =============================================================================
-- 7. Statistiche e monitoring views
-- =============================================================================
-- Crea view utili per monitoring e debugging

-- View per query lente
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time / 1000.0 AS total_seconds,
    mean_time / 1000.0 AS mean_seconds,
    (total_time / sum(total_time) OVER()) * 100 AS percent_total_time
FROM pg_stat_statements 
WHERE calls > 10  -- Solo query chiamate più di 10 volte
ORDER BY mean_time DESC;

-- View per statistiche tabelle
CREATE OR REPLACE VIEW table_stats AS
SELECT 
    schemaname,
    tablename,
    n_tup_ins AS inserts,
    n_tup_upd AS updates,
    n_tup_del AS deletes,
    n_live_tup AS live_tuples,
    n_dead_tup AS dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
ORDER BY n_live_tup DESC;

-- =============================================================================
-- 8. Funzioni di utilità per monitoring
-- =============================================================================

-- Funzione per reset statistiche pg_stat_statements
CREATE OR REPLACE FUNCTION reset_query_stats()
RETURNS void AS $$
BEGIN
    PERFORM pg_stat_statements_reset();
    RAISE NOTICE 'Query statistics reset successfully';
END;
$$ LANGUAGE plpgsql;

-- Funzione per ottenere top query lente
CREATE OR REPLACE FUNCTION get_slow_queries(limit_count INTEGER DEFAULT 10)
RETURNS TABLE(
    query_text TEXT,
    call_count BIGINT,
    total_time_seconds NUMERIC,
    mean_time_seconds NUMERIC,
    percent_of_total NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sq.query::TEXT,
        sq.calls,
        sq.total_seconds,
        sq.mean_seconds,
        sq.percent_total_time
    FROM slow_queries sq
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 9. Commenti e documentazione
-- =============================================================================

COMMENT ON EXTENSION pg_stat_statements IS 'Query performance statistics tracking';
COMMENT ON VIEW slow_queries IS 'Top slow queries with execution statistics';
COMMENT ON VIEW table_stats IS 'Table usage and maintenance statistics';
COMMENT ON FUNCTION reset_query_stats() IS 'Reset pg_stat_statements statistics';
COMMENT ON FUNCTION get_slow_queries(INTEGER) IS 'Get top N slow queries';

-- =============================================================================
-- 10. Verifica finale
-- =============================================================================

-- Log degli indici creati
DO $$
DECLARE
    index_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO index_count 
    FROM pg_indexes 
    WHERE indexname LIKE 'idx_%' 
    AND schemaname = 'public';
    
    RAISE NOTICE 'Migration 002_perf.sql completata con successo!';
    RAISE NOTICE 'Indici creati: %', index_count;
    RAISE NOTICE 'Estensioni abilitate: pg_stat_statements';
    RAISE NOTICE 'Views create: slow_queries, table_stats';
    RAISE NOTICE 'Funzioni create: reset_query_stats(), get_slow_queries()';
END $$;
