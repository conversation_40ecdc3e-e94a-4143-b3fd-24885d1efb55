#!/usr/bin/env bash
# =============================================================
# Script: check_env_vars.sh
# Scopo : Verifica che tutte le variabili d'ambiente necessarie
#         per il deployment siano configurate correttamente
# Autore: ReBuild Link Team
# Corso : DevOps per Web App – lezione "Environment Variables"
# -------------------------------------------------------------
# Questo script controlla che le variabili d'ambiente richieste
# per il funzionamento dell'applicazione siano presenti e
# abbiano un formato valido.
#
# Utile prima di:
#   • Eseguire il deployment su Vercel
#   • Configurare GitHub Actions
#   • Avviare l'applicazione in produzione
#
# =============================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

echo "🔍 Verifica Variabili d'Ambiente per ReBuild Link"
echo "================================================="
echo ""

# -----------------------------
# 1. Definizione variabili richieste
# -----------------------------
# Array associativo con variabili e loro descrizioni
declare -A REQUIRED_VARS=(
    ["DATABASE_URL"]="Connessione PostgreSQL (AWS RDS)"
    ["SUPABASE_URL"]="URL progetto Supabase"
    ["SUPABASE_ANON_KEY"]="Chiave pubblica Supabase (client-side)"
)

# Variabili opzionali ma raccomandate
declare -A OPTIONAL_VARS=(
    ["SUPABASE_SERVICE_ROLE_KEY"]="Chiave service role Supabase (server-side)"
    ["NEXT_PUBLIC_APP_URL"]="URL base applicazione"
    ["NODE_ENV"]="Ambiente di esecuzione (development/production)"
)

# -----------------------------
# 2. Funzioni di validazione
# -----------------------------

# Valida formato DATABASE_URL
validate_database_url() {
    local url="$1"
    if [[ "$url" =~ ^postgres(ql)?://[^:]+:[^@]+@[^:]+:[0-9]+/[^/]+$ ]]; then
        return 0
    else
        return 1
    fi
}

# Valida formato SUPABASE_URL
validate_supabase_url() {
    local url="$1"
    if [[ "$url" =~ ^https://[a-zA-Z0-9-]+\.supabase\.co$ ]]; then
        return 0
    else
        return 1
    fi
}

# Valida formato JWT (Supabase keys)
validate_jwt_token() {
    local token="$1"
    if [[ "$token" =~ ^eyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$ ]]; then
        return 0
    else
        return 1
    fi
}

# -----------------------------
# 3. Controllo variabili richieste
# -----------------------------
echo "1️⃣  Controllo variabili richieste..."
echo ""

all_required_present=true
for var in "${!REQUIRED_VARS[@]}"; do
    description="${REQUIRED_VARS[$var]}"
    
    if [[ -n "${!var:-}" ]]; then
        value="${!var}"
        preview="${value:0:30}..."
        
        # Validazione specifica per tipo di variabile
        case "$var" in
            "DATABASE_URL")
                if validate_database_url "$value"; then
                    echo "   ✅ $var ($description)"
                    echo "      Preview: $preview"
                else
                    echo "   ❌ $var - Formato non valido"
                    echo "      Formato atteso: postgres://user:pass@host:port/db"
                    all_required_present=false
                fi
                ;;
            "SUPABASE_URL")
                if validate_supabase_url "$value"; then
                    echo "   ✅ $var ($description)"
                    echo "      Preview: $preview"
                else
                    echo "   ❌ $var - Formato non valido"
                    echo "      Formato atteso: https://project-id.supabase.co"
                    all_required_present=false
                fi
                ;;
            "SUPABASE_ANON_KEY")
                if validate_jwt_token "$value"; then
                    echo "   ✅ $var ($description)"
                    echo "      Preview: ${value:0:20}...${value: -10}"
                else
                    echo "   ❌ $var - Formato JWT non valido"
                    echo "      Formato atteso: eyJ... (JWT token)"
                    all_required_present=false
                fi
                ;;
            *)
                echo "   ✅ $var ($description)"
                echo "      Preview: $preview"
                ;;
        esac
    else
        echo "   ❌ $var - NON IMPOSTATA ($description)"
        all_required_present=false
    fi
    echo ""
done

# -----------------------------
# 4. Controllo variabili opzionali
# -----------------------------
echo "2️⃣  Controllo variabili opzionali..."
echo ""

for var in "${!OPTIONAL_VARS[@]}"; do
    description="${OPTIONAL_VARS[$var]}"
    
    if [[ -n "${!var:-}" ]]; then
        value="${!var}"
        
        case "$var" in
            "SUPABASE_SERVICE_ROLE_KEY")
                if validate_jwt_token "$value"; then
                    echo "   ✅ $var ($description)"
                    echo "      Preview: ${value:0:20}...${value: -10}"
                else
                    echo "   ⚠️  $var - Formato JWT non valido"
                fi
                ;;
            "NEXT_PUBLIC_APP_URL")
                if [[ "$value" =~ ^https?://[^/]+$ ]]; then
                    echo "   ✅ $var ($description)"
                    echo "      Preview: $value"
                else
                    echo "   ⚠️  $var - Formato URL non valido"
                fi
                ;;
            *)
                preview="${value:0:30}..."
                echo "   ✅ $var ($description)"
                echo "      Preview: $preview"
                ;;
        esac
    else
        echo "   ⚠️  $var - Non impostata ($description)"
    fi
    echo ""
done

# -----------------------------
# 5. Test connessione database (opzionale)
# -----------------------------
if [[ -n "${DATABASE_URL:-}" ]] && validate_database_url "${DATABASE_URL}"; then
    echo "3️⃣  Test connessione database..."
    
    if command -v psql >/dev/null 2>&1; then
        echo "   🔍 Tentativo di connessione al database..."
        if timeout 10 psql "${DATABASE_URL}" -c "SELECT 1;" >/dev/null 2>&1; then
            echo "   ✅ Connessione database riuscita"
        else
            echo "   ⚠️  Connessione database fallita"
            echo "      Verifica che l'istanza RDS sia attiva e accessibile"
        fi
    else
        echo "   ⚠️  psql non disponibile, salto test connessione"
    fi
    echo ""
fi

# -----------------------------
# 6. Riepilogo finale
# -----------------------------
echo "📊 Riepilogo finale:"
echo ""

if $all_required_present; then
    echo "✅ Tutte le variabili richieste sono presenti e valide"
    echo ""
    echo "🚀 L'applicazione è pronta per:"
    echo "   • Deployment su Vercel"
    echo "   • Configurazione GitHub Actions"
    echo "   • Esecuzione in produzione"
    echo ""
    echo "💡 Prossimi passi suggeriti:"
    echo "   1. ./scripts/set_github_secrets.sh  # Configura GitHub secrets"
    echo "   2. Configura le stesse variabili su Vercel dashboard"
    echo "   3. Testa il deployment"
else
    echo "❌ Alcune variabili richieste mancano o non sono valide"
    echo ""
    echo "🔧 Azioni necessarie:"
    echo "   1. Configura le variabili mancanti"
    echo "   2. Verifica i formati delle variabili non valide"
    echo "   3. Riesegui questo script per verificare"
    echo ""
    echo "📚 Guide utili:"
    echo "   • Database: ./scripts/check_rds_status.sh"
    echo "   • Supabase: Dashboard > Settings > API"
    echo "   • Documentazione: cat README.md"
fi

echo ""
echo "🔗 Per maggiori informazioni: cat scripts/README.md"
