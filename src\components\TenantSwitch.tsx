"use client";

import { useRouter, usePathname } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

/**
 * Componente per il cambio tenant (ua/it)
 * Deriva il tenant corrente dal pathname e permette di cambiarlo
 */
export default function TenantSwitch() {
  const router = useRouter();
  const pathname = usePathname();
  
  // Estrae il tenant dal pathname (primo segmento dopo /)
  const currentTenant = pathname.split('/')[1] || 'it';
  
  const handleTenantChange = (value: string) => {
    // Mantiene il resto del path sostituendo solo il tenant
    const pathSegments = pathname.split('/');
    pathSegments[1] = value;
    const newPath = pathSegments.join('/');
    router.push(newPath);
  };

  return (
    <Select value={currentTenant} onValueChange={handleTenantChange}>
      <SelectTrigger className="w-20">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="ua">🇺🇦 UA</SelectItem>
        <SelectItem value="it">🇮🇹 IT</SelectItem>
      </SelectContent>
    </Select>
  );
}
