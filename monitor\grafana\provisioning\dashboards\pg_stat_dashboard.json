{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "PostgreSQL Performance Insights per ReBuild Link - Monitoring query performance, connections e statistiche database", "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "total_s"}, "properties": [{"id": "unit", "value": "s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "mean_s"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "mean_s"}]}, "pluginVersion": "10.4.1", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  LEFT(query, 100) || '...' AS query_preview,\n  calls,\n  ROUND(total_time/1000.0, 3) AS total_s,\n  ROUND(mean_time/1000.0, 3) AS mean_s,\n  ROUND((total_time / sum(total_time) OVER()) * 100, 2) AS percent_total\nFROM pg_stat_statements \nWHERE calls > 5\nORDER BY mean_time DESC \nLIMIT 10;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "🐌 Top 10 Slow Queries", "type": "table"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "10.4.1", "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  NOW() as time,\n  numbackends as \"Active Connections\"\nFROM pg_stat_database \nWHERE datname = current_database();", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "🔗 Database Connections", "type": "graph"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 3, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "live_tuples"}]}, "pluginVersion": "10.4.1", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  tablename,\n  n_live_tup as live_tuples,\n  n_dead_tup as dead_tuples,\n  n_tup_ins as inserts,\n  n_tup_upd as updates,\n  n_tup_del as deletes,\n  last_autovacuum\nFROM pg_stat_user_tables \nORDER BY n_live_tup DESC\nLIMIT 10;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "📊 Table Statistics", "type": "table"}, {"datasource": "PostgreSQL", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "id": 4, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "size_mb"}]}, "pluginVersion": "10.4.1", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  schemaname,\n  tablename,\n  ROUND(pg_total_relation_size(schemaname||'.'||tablename) / 1024.0 / 1024.0, 2) AS size_mb,\n  ROUND(pg_relation_size(schemaname||'.'||tablename) / 1024.0 / 1024.0, 2) AS table_size_mb,\n  ROUND((pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) / 1024.0 / 1024.0, 2) AS index_size_mb\nFROM pg_tables \nWHERE schemaname = 'public'\nORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "💾 Database Size by Table", "type": "table"}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["postgresql", "performance", "rebuild-link"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ReBuild Link – Postgres Insights", "uid": "rebuild-link-postgres", "version": 1, "weekStart": ""}