// 📄 src/components/LocaleToggle.tsx
// Bottone per cambiare lingua globale (en/ua) tramite LocaleContext. UI semplice, pronto per header.

"use client";
import { useLocale } from "@/components/LocaleContext";
const LocaleToggle = () => {
  const { locale, setLocale } = useLocale();
  return (
    <button
      className="rounded-full border px-3 py-1 text-sm hover:bg-gray-100 transition"
      onClick={() => setLocale(locale === "en" ? "ua" : "en")}
      aria-label="Toggle language"
    >
      {locale === "en" ? "UA" : "EN"}
    </button>
  );
};
export default LocaleToggle;
