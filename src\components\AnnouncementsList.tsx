"use client";
import useSWR from 'swr';

type Announcement = {
  id: number;
  title: string;
  type: 'project' | 'job';
  region?: string;
  location?: string;
};

interface AnnouncementsListProps {
  tenantSlug: string;
}

const fetcher = (tenantSlug: string) => {
  // Usa query params invece di header (più pulito)
  // Il mapping slug → tenant_id è gestito lato server nell'API route
  return fetch(`/api/announcements?tenant=${tenantSlug}`).then(r => r.json());
};

export default function AnnouncementsList({ tenantSlug }: AnnouncementsListProps) {
  const { data, error, isLoading } = useSWR<Announcement[]>(
    tenantSlug ? `announcements-${tenantSlug}` : null,
    () => fetcher(tenantSlug)
  );

  if (isLoading) return <div>Caricamento…</div>;
  if (error) return <div>Errore nel caricamento degli annunci.</div>;
  if (!data || !Array.isArray(data)) return <p>N<PERSON>un annuncio.</p>;

  return (
    <ul className="space-y-4">
      {data.map((a: Announcement) => (
        <li
          key={a.id}
          className="border border-neutral-700 p-4 rounded-lg hover:bg-neutral-800"
        >
          <p className="font-semibold">{a.title}</p>
          <p className="text-sm text-neutral-400">
            {a.type === 'project' ? `Regione: ${a.region}` : `Luogo: ${a.location}`}
          </p>
        </li>
      ))}
    </ul>
  );
}
