// 📄 src/components/SkipLink.tsx
// Skip link per accessibilità - permette agli utenti con screen reader
// di saltare direttamente al contenuto principale

"use client";
import { useLocale } from "@/components/LocaleContext";

/**
 * Componente skip link per accessibilità
 * Invisibile di default, visibile quando riceve focus (Tab)
 * Permette di saltare direttamente al contenuto principale
 */
export default function SkipLink() {
  const { t } = useLocale();

  return (
    <a 
      href="#main" 
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-700 text-white rounded px-3 py-2 z-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
    >
      {t("skip_to_content")}
    </a>
  );
}
