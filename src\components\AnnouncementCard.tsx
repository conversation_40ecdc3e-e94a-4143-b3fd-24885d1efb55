import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Calendar } from 'lucide-react';
import { useLocale } from '@/components/LocaleContext';

interface AnnouncementCardProps {
  id: number;
  title: string;
  type: 'project' | 'job';
  region?: string;
  location?: string;
  description?: string;
  price?: number;
  createdAt?: string;
}

/**
 * Card per visualizzare un singolo annuncio
 * Design compatto con informazioni essenziali
 */
export default function AnnouncementCard({
  // id, // Commented out as not used in current implementation
  title,
  type,
  region,
  location,
  description,
  price,
  createdAt
}: Omit<AnnouncementCardProps, 'id'>) {
  const { t } = useLocale();
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'project': return 'bg-primary-700 text-white';
      case 'job': return 'bg-accent-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'project': return 'Progetto';
      case 'job': return 'Lavoro';
      default: return type;
    }
  };

  return (
    <Card
      className="h-64 hover:shadow-md transition-shadow cursor-pointer"
      role="listitem"
      aria-label={`${getTypeLabel(type)}: ${title}`}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start mb-2">
          <Badge className={getTypeColor(type)}>
            <span aria-label={`${t("category_label")} ${getTypeLabel(type)}`}>
              {getTypeLabel(type)}
            </span>
          </Badge>
          {price && (
            <span className="text-lg font-semibold text-primary-700">
              €{price.toLocaleString()}
            </span>
          )}
        </div>
        <CardTitle className="text-lg line-clamp-2">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {description && (
          <p className="text-sm text-gray-600 line-clamp-3 mb-3">
            {description}
          </p>
        )}
        <div className="flex items-center justify-between text-xs text-gray-500">
          {(location || region) && (
            <div className="flex items-center gap-1">
              <MapPin className="w-3 h-3" />
              <span>{location || region}</span>
            </div>
          )}
          {createdAt && (
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{new Date(createdAt).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
