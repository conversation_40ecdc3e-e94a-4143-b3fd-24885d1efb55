import { defineConfig, devices } from '@playwright/test';

/**
 * Configurazione Playwright per test E2E
 * 
 * Questo file configura Playwright per eseguire test end-to-end
 * sull'applicazione ReBuild Link, testando funzionalità critiche
 * come autenticazione, navigazione multi-tenant e CRUD annunci.
 */
export default defineConfig({
  // Directory contenente i test E2E
  testDir: './tests/e2e',

  // Configurazione globale per tutti i test
  use: {
    // URL base dell'applicazione
    baseURL: "http://localhost:3000",

    // Viewport per test consistenti
    viewport: { width: 1280, height: 800 },

    // Esegui in modalità headless (senza interfaccia grafica)
    headless: true,

    // Registra trace per debugging in caso di retry
    trace: 'on-first-retry',

    // Screenshot solo in caso di fallimento
    screenshot: 'only-on-failure',

    // Registra video solo in caso di fallimento
    video: 'retain-on-failure',
  },

  // Server di sviluppo locale
  // Playwright avvia automaticamente il server per i test
  webServer: {
    command: "npm run dev",
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});
