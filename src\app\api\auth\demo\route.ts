import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

/**
 * API Route per demo authentication nei test E2E
 * POST /api/auth/demo crea sessione demo e restituisce success
 */
export async function POST(_req: NextRequest) {
  // Solo in development
  if (process.env.NODE_ENV !== "development") {
    return NextResponse.json({ error: "Demo auth not available in production" }, { status: 403 });
  }

  console.log("🎭 Creating demo auth session for E2E tests");
  
  // Crea sessione demo user
  const demoUser = {
    id: "demo-user-e2e",
    email: "<EMAIL>",
    name: "Demo User E2E",
    tenant_id: "ua"
  };
  
  // Imposta cookie demo
  const cookieStore = await cookies();
  cookieStore.set("demo-auth", JSON.stringify(demoUser), {
    httpOnly: false, // Accessibile da client per test
    secure: false,   // HTTP ok in development
    maxAge: 3600,    // 1 ora
    path: "/"
  });
  
  return NextResponse.json({ 
    success: true, 
    user: demoUser,
    message: "Demo auth session created" 
  });
}
