import { z } from 'zod';

// Schema aggiornato per supportare i nuovi campi del form v2
export const announcementSchema = z.object({
  type: z.enum(['Project', 'Job']),
  title: z.string().min(5, "Almeno 5 caratteri"),
  description: z.string().min(20, "Almeno 20 caratteri"),
  region: z.string().optional(),
  budgetMin: z.coerce.number().positive().optional(),
  budgetMax: z.coerce.number().positive().optional(),
  location: z.string().optional(),
  salary: z.string().optional(),
  availability: z.string().optional(),
}).refine(data => (
  data.type === "Project"
    ? data.region && data.budgetMin && data.budgetMax
    : data.location && data.salary && data.availability
), { message: "Compila tutti i campi del tipo selezionato" });

export function validateAnnouncement(data: unknown) {
  return announcementSchema.safeParse(data);
}
