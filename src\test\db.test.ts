// Test unitari per la funzione db.query() (src/lib/db.ts)
// Questi test verificano che la funzione query esegua correttamente query SQL e restituisca i risultati attesi.
// Ogni test viene isolato tramite transazione (BEGIN/ROLLBACK) per non modificare il database.
// Collegato alla pagina: backend/db-helper

import { query } from '../lib/db';

describe('db.query', () => {
  beforeEach(async () => {
    await query('BEGIN');
  });
  afterEach(async () => {
    await query('ROLLBACK');
  });

  it('ritorna il risultato corretto per SELECT 1 AS val', async () => {
    const res = await query<{ val: number }>('SELECT 1 AS val');
    expect(res).toEqual([{ val: 1 }]);
  });
});
