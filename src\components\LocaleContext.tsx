// 📄 src/components/LocaleContext.tsx
// Context React per gestione lingua (en/ua) globale, con hook e provider.
// Salva la scelta in localStorage, fornisce funzione t() per traduzioni future.

"use client";
import React, { createContext, useContext, useEffect, useState } from "react";
import en from "@/i18n/en";
import ua from "@/i18n/ua";
const dict = { en, ua };
type Locale = keyof typeof dict;
interface LocaleContextType {
  locale: Locale;
  setLocale: (l: Locale) => void;
  t: (k: string) => string;
}
const Ctx = createContext<LocaleContextType | null>(null);

export const LocaleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [locale, setLocale] = useState<Locale>(() =>
    typeof window !== "undefined"
      ? ((localStorage.getItem("locale") as Locale) ?? "en")
      : "en"
  );
  // Salva locale in localStorage
  useEffect(() => { localStorage.setItem("locale", locale); }, [locale]);

  // Aggiorna attributo lang del documento per accessibilità
  useEffect(() => {
    document.documentElement.lang = locale;
  }, [locale]);

  // Helper per traduzioni
  type DictType = typeof en;
  const t = (k: string) => {
    const d = dict[locale] as DictType;
    return d.common[k as keyof typeof d.common] ?? k;
  };
  return <Ctx.Provider value={{ locale, setLocale, t }}>{children}</Ctx.Provider>;
};
export const useLocale = () => useContext(Ctx)!;
