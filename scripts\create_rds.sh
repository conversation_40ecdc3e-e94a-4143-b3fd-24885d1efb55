#!/usr/bin/env bash
# ============================================================
# Script: create_rds.sh
# Scopo : Provisioning di un'istanza Amazon RDS PostgreSQL 15
# Autore: ReBuild Link Team
# Corso : DevOps per Web App – lezione "Database in cloud"
# ------------------------------------------------------------
# Questo script mostra passo-passo come creare gratuitamente
# (free-tier) un'istanza RDS, configurare la sicurezza e
# ottenere un database pronto per la tua applicazione.
#
# 👉 Prerequisiti:
#    • AWS CLI configurata (`aws configure`)
#    • Variabile d'ambiente DB_PASSWORD con la password del
#      super-user
#
# ============================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

# -------------------------------
# 1. Parametri personalizzabili
# -------------------------------
DB_INSTANCE_ID="${DB_INSTANCE_ID:-rebuildlink-db}"  # Nome istanza RDS
DB_NAME="${DB_NAME:-rebuildlink}"                   # Nome database
DB_USER="${DB_USER:-rebuildlink_user}"              # Utente master
DB_PASSWORD="${DB_PASSWORD:?❌  Esporta prima DB_PASSWORD}"  # Password
AWS_REGION="${AWS_REGION:-eu-central-1}"            # Regione AWS (Frankfurt)
SG_NAME="${SG_NAME:-${DB_INSTANCE_ID}-sg}"          # Security Group

# -------------------------------
# 2. Rileva il tuo IP pubblico
# -------------------------------
echo "📡 Rilevo il tuo IP pubblico..."
MY_IP=$(curl -s https://checkip.amazonaws.com)
CIDR="$MY_IP/32"
echo "   Concedo accesso solo a $CIDR"

# -------------------------------
# 3. Crea/riutilizza la Security Group
# -------------------------------
echo "🔐 Verifico la Security Group $SG_NAME..."
SG_ID=$(aws ec2 describe-security-groups \
          --filters Name=group-name,Values="$SG_NAME" \
          --query 'SecurityGroups[0].GroupId' --output text 2>/dev/null || true)

if [[ -z "$SG_ID" || "$SG_ID" == "None" ]]; then
  echo "   Creazione nuova SG..."
  VPC_ID=$(aws ec2 describe-vpcs --query 'Vpcs[0].VpcId' --output text)
  SG_ID=$(aws ec2 create-security-group \
            --group-name "$SG_NAME" \
            --description "SG per $DB_INSTANCE_ID" \
            --vpc-id "$VPC_ID" --output text)

  aws ec2 authorize-security-group-ingress \
      --group-id "$SG_ID" \
      --protocol tcp --port 5432 --cidr "$CIDR"
  echo "   SG $SG_ID creata e regola ingress aggiunta ✅"
else
  echo "   Uso SG esistente $SG_ID ✅"
fi

# -------------------------------
# 4. Creazione dell'istanza RDS
# -------------------------------
echo "🚀 Avvio provisioning RDS $DB_INSTANCE_ID..."
aws rds create-db-instance \
  --db-instance-identifier "$DB_INSTANCE_ID" \
  --db-name "$DB_NAME" \
  --allocated-storage 20 \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --engine-version 15 \
  --master-username "$DB_USER" \
  --master-user-password "$DB_PASSWORD" \
  --vpc-security-group-ids "$SG_ID" \
  --publicly-accessible \
  --backup-retention-period 7 \
  --storage-type gp2 \
  --region "$AWS_REGION"

echo "🎉 Provisioning avviato! L'istanza impiega ~10 minuti per diventare AVAILABLE."
echo "   Controlla lo stato con: aws rds describe-db-instances --db-instance-identifier $DB_INSTANCE_ID"

# -------------------------------
# 5. Attendi che l'istanza sia disponibile (opzionale)
# -------------------------------
echo ""
echo "⏳ Vuoi attendere che l'istanza sia pronta? (y/N)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    echo "   Attendo che l'istanza $DB_INSTANCE_ID diventi AVAILABLE..."
    aws rds wait db-instance-available --db-instance-identifier "$DB_INSTANCE_ID"
    
    # Ottieni l'endpoint dell'istanza
    ENDPOINT=$(aws rds describe-db-instances \
                 --db-instance-identifier "$DB_INSTANCE_ID" \
                 --query 'DBInstances[0].Endpoint.Address' \
                 --output text)
    
    echo ""
    echo "🎯 Istanza RDS pronta!"
    echo "   Endpoint: $ENDPOINT"
    echo "   Database: $DB_NAME"
    echo "   Username: $DB_USER"
    echo ""
    echo "📝 La tua DATABASE_URL è:"
    echo "   ***********************************************/$DB_NAME"
    echo ""
    echo "💡 Aggiungi questa variabile al tuo .env.local per la produzione!"
fi

echo ""
echo "✨ Script completato con successo!"
