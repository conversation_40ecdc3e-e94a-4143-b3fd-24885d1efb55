/**
 * Configurazione Jest per supportare TypeScript e import ES6.
 * Usa ts-jest come transformer per i file .ts/.tsx.
 * Permette di eseguire test moderni in progetti Next.js/TypeScript.
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  testMatch: ['**/src/test/**/*.test.(ts|tsx)'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  setupFiles: ['dotenv/config'],
};
