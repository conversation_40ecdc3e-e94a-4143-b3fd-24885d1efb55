// 📄 src/components/ProtectedPage.tsx
// HOC React per proteggere pagine/route: redirect automatico su /signin se non autenticato.
// Usa Supabase FE client, router Next.js, e spinner di caricamento. Utile per dashboard, profili, ecc.
// Esempio d'uso:
//   import withProtected from "@/components/ProtectedPage";
//   function Dashboard() { ... }
//   export default withProtected(Dashboard);

"use client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";

export default function withProtected<P extends object>(
  Page: React.ComponentType<P>
): React.FC<P> {
  return function Protected(props: P) {
    const router = useRouter();
    const [allowed, setAllowed] = useState<boolean | null>(null); // null = loading

    useEffect(() => {
      supabase.auth.getSession().then(({ data }) => {
        if (!data.session) {
          router.replace("/signin");
        } else {
          setAllowed(true);
        }
      });
    }, [router]);

    if (allowed === null) {
      return (
        <div className="flex h-screen w-screen items-center justify-center">
          <span className="animate-spin rounded-full border-4 border-gray-300 border-t-transparent h-8 w-8" />
        </div>
      );
    }

    return <Page {...props} />;
  };
}

// Alias export per import più leggibile
export { withProtected as ProtectedPage };
