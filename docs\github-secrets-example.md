# GitHub Secrets Configuration per RDS Monitoring

Questo documento spiega come configurare i secrets GitHub necessari per abilitare il monitoraggio automatico RDS con CloudWatch.

## 🔑 Secrets Richiesti

### Database & Supabase (già esistenti)
```bash
gh secret set DATABASE_URL --body "******************************/db"
gh secret set SUPABASE_URL --body "https://your-project.supabase.co"
gh secret set SUPABASE_ANON_KEY --body "eyJhbGciOi..."
gh secret set SUPABASE_SERVICE_ROLE_KEY --body "eyJhbGciOi..."
```

### Vercel (già esistenti)
```bash
gh secret set VERCEL_TOKEN --body "your-vercel-token"
gh secret set VERCEL_ORG_ID --body "your-org-id"
gh secret set VERCEL_PROJECT_ID --body "your-project-id"
```

### AWS per RDS Monitoring (NUOVI)
```bash
# ID dell'istanza RDS da monitorare
gh secret set RDS_INSTANCE_ID --body "rebuildlink-db"

# Email per ricevere notifiche di allarme
gh secret set ALERT_EMAIL --body "<EMAIL>"

# Credenziali AWS per CloudWatch
gh secret set AWS_ACCESS_KEY_ID --body "AKIA..."
gh secret set AWS_SECRET_ACCESS_KEY --body "..."
gh secret set AWS_DEFAULT_REGION --body "eu-central-1"
```

## 🛠️ Come Ottenere le Credenziali AWS

### 1. Crea un utente IAM per CI/CD

```bash
# Accedi alla console AWS IAM
# https://console.aws.amazon.com/iam/

# Crea nuovo utente:
# - Nome: github-actions-rds-monitor
# - Tipo accesso: Programmatic access
```

### 2. Assegna le policy necessarie

Policy minime richieste:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "cloudwatch:PutMetricAlarm",
                "cloudwatch:DescribeAlarms",
                "sns:CreateTopic",
                "sns:Subscribe",
                "sns:GetTopicAttributes"
            ],
            "Resource": "*"
        }
    ]
}
```

### 3. Salva le credenziali

Dopo aver creato l'utente, AWS ti fornirà:
- **Access Key ID**: Inizia con `AKIA...`
- **Secret Access Key**: Stringa lunga alfanumerica

⚠️ **IMPORTANTE**: Salva queste credenziali immediatamente, non potrai più visualizzarle!

## 🔍 Verifica Configurazione

Dopo aver configurato tutti i secrets, verifica che siano presenti:

```bash
# Lista tutti i secrets del repository
gh secret list

# Output atteso:
# AWS_ACCESS_KEY_ID
# AWS_DEFAULT_REGION
# AWS_SECRET_ACCESS_KEY
# ALERT_EMAIL
# DATABASE_URL
# RDS_INSTANCE_ID
# SUPABASE_ANON_KEY
# SUPABASE_SERVICE_ROLE_KEY
# SUPABASE_URL
# VERCEL_ORG_ID
# VERCEL_PROJECT_ID
# VERCEL_TOKEN
```

## 🚀 Test del Monitoraggio

Una volta configurati i secrets, testa il sistema:

```bash
# 1. Fai un commit su main per attivare il workflow
echo "# Test monitoring" >> README.md
git add .
git commit -m "test: attiva monitoraggio RDS"
git push origin main

# 2. Monitora il workflow su GitHub
# https://github.com/TUO-USERNAME/REPO/actions

# 3. Controlla la tua email per la conferma SNS
# Clicca "Confirm subscription" quando arriva
```

## 📧 Gestione Email di Notifica

### Conferma Iscrizione
1. Dopo il primo deploy, riceverai: "AWS Notification - Subscription Confirmation"
2. Clicca il link "Confirm subscription"
3. Vedrai: "Subscription confirmed!"

### Formato Allarmi
```
Subject: ALARM: rebuildlink-db-HighCPU in EU-CENTRAL-1

AlarmName: rebuildlink-db-HighCPU
AlarmDescription: RDS CPU utilization is too high
NewStateValue: ALARM
NewStateReason: Threshold Crossed: 1 out of 1 datapoints [85.2] was greater than the threshold (80.0).
StateChangeTime: 2025-01-09T14:30:00.000Z
```

## 🔧 Troubleshooting

### Workflow fallisce con errori AWS
```bash
# Verifica che le credenziali AWS siano corrette
aws sts get-caller-identity

# Controlla che l'utente IAM abbia le policy necessarie
aws iam list-attached-user-policies --user-name github-actions-rds-monitor
```

### Non ricevo email di conferma SNS
```bash
# Controlla che ALERT_EMAIL sia configurata correttamente
gh secret list | grep ALERT_EMAIL

# Verifica nella console AWS SNS che il topic sia stato creato
# https://console.aws.amazon.com/sns/
```

### Allarme non si attiva
```bash
# Controlla che RDS_INSTANCE_ID corrisponda al nome reale
aws rds describe-db-instances --query 'DBInstances[].DBInstanceIdentifier'

# Verifica che l'allarme sia stato creato
aws cloudwatch describe-alarms --alarm-names "rebuildlink-db-HighCPU"
```

## 🎯 Best Practices

- ✅ **Usa email dedicata** per gli allarmi (es: <EMAIL>)
- ✅ **Testa sempre** dopo aver configurato i secrets
- ✅ **Monitora i costi** AWS per CloudWatch e SNS
- ✅ **Ruota periodicamente** le credenziali AWS
- ✅ **Documenta** eventuali modifiche alle soglie di allarme
