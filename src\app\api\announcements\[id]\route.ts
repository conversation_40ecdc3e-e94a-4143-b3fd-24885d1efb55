// 📄 src/app/api/announcements/[id]/route.ts
// API route per dettaglio singolo annuncio - accesso diretto al database
import { NextRequest, NextResponse } from "next/server";
import { query } from '@/lib/db';

// GET ➜ fetch single announcement
// NOTA: Next.js 15.3.5 richiede parametri asincroni anche nelle API routes
export async function GET(_req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  // Validazione ID
  const announcementId = parseInt(id);
  if (isNaN(announcementId)) {
    return NextResponse.json({ error: 'ID annuncio non valido' }, { status: 400 });
  }

  try {

    // Query per ottenere il dettaglio dell'annuncio
    const rows = await query(
      `SELECT id,
              title,
              description,
              announcement_type AS type,
              region,
              location,
              budget_min,
              budget_max,
              salary_range,
              availability,
              created_at
       FROM announcements
       WHERE id = $1`,
      [announcementId]
    );

    if (rows.length === 0) {
      return NextResponse.json({ error: 'Annuncio non trovato' }, { status: 404 });
    }

    return NextResponse.json(rows[0]);
  } catch (error) {
    console.error('Errore nel recupero annuncio:', error);

    // Fallback con dati mock se il DB non è disponibile
    const mockAnnouncement = {
      id: announcementId,
      title: `Annuncio Mock ${announcementId}`,
      description: 'Questa è una descrizione di esempio per testare la chat realtime. Il database non è configurato, quindi vengono mostrati dati mock.',
      type: 'project',
      region: 'Kyiv',
      budget_min: 1000,
      budget_max: 5000,
      created_at: new Date().toISOString()
    };

    return NextResponse.json(mockAnnouncement);
  }
}

// PUT e DELETE saranno implementati in futuro se necessario
