
import type { Metadata } from 'next'
import './globals.css'
import { LocaleProvider } from "@/components/LocaleContext";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import SkipLink from "@/components/SkipLink";

export const metadata: Metadata = {
  title: 'Rebuild Link',
  description: 'Piattaforma per annunci e collegamenti',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="it">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com"/>
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous"/>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Poppins:wght@600;700&family=Fira+Code:wght@400;600&display=swap" rel="stylesheet"/>
      </head>
      <body className="font-body antialiased">
        <LocaleProvider>
          <SkipLink />
          <Navbar />
          <main id="main" tabIndex={-1} className="min-h-screen">
            {children}
          </main>
          <Footer />
        </LocaleProvider>
      </body>
    </html>
  )
}
