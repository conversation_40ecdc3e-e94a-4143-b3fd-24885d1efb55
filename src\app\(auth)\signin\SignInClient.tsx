"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Auth } from "@supabase/auth-ui-react";
import { ThemeSupa } from "@supabase/auth-ui-shared";
import { supabase } from "@/lib/supabaseClient";

/**
 * Client Component per Supabase Auth UI
 * Gestisce demo mode per test E2E
 */
export default function SignInClient({ demoMode = false }: { demoMode?: boolean }) {
  const router = useRouter();

  // Gestisce demo mode per test E2E
  useEffect(() => {
    if (demoMode && process.env.NODE_ENV === "development") {
      console.log("🎭 Demo mode: creating auth session...");

      fetch("/api/auth/demo", { method: "POST" })
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            console.log("✅ Demo auth created, redirecting...");
            router.push("/");
          } else {
            console.error("❌ Demo auth failed:", data);
          }
        })
        .catch(error => {
          console.error("❌ Demo auth error:", error);
        });
    }
  }, [demoMode, router]);
  return (
    <main className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <div className="rounded-xl shadow p-6 w-full max-w-md bg-white">
        <h1 className="text-2xl font-bold text-center mb-6 text-primary-700">
          ReBuild Link
        </h1>
        <p className="text-gray-600 text-center mb-6">
          Accedi alla piattaforma per gestire i tuoi annunci
        </p>
        
        <Auth
          supabaseClient={supabase}
          appearance={{ theme: ThemeSupa }}
          theme="default"
          providers={[]}
          socialLayout="horizontal"
          redirectTo={typeof window !== "undefined" ? `${window.location.origin}/` : undefined}
          magicLink
          onlyThirdPartyProviders={false}
          localization={{ 
            variables: { 
              sign_in: { 
                email_label: "Email", 
                password_label: "Password",
                button_label: "Accedi",
                loading_button_label: "Accesso in corso..."
              },
              sign_up: {
                email_label: "Email",
                password_label: "Password", 
                button_label: "Registrati",
                loading_button_label: "Registrazione in corso..."
              }
            } 
          }}
          view="sign_in"
        />
        
        {/* Info per sviluppatori */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
            <strong>Dev Mode:</strong> Usa <code>/signin?demo=true</code> per test E2E
          </div>
        )}
      </div>
    </main>
  );
}
