#!/usr/bin/env bash
# =============================================================
# Script: rds_monitor.sh
# Scopo : Creare / aggiornare un allarme CloudWatch per RDS
#         (CPUUtilization > 80 % per 5 min) con notifica SNS.
# -------------------------------------------------------------
# Variabili richieste:
#   RDS_INSTANCE_ID        – identificatore istanza RDS
#
# Variabili opzionali:
#   SNS_TOPIC_NAME         – default: rds-alerts
#   ALERT_EMAIL            – email da iscrivere al topic
# =============================================================
set -euo pipefail

: "${RDS_INSTANCE_ID:?❌  RDS_INSTANCE_ID non impostato}"

SNS_TOPIC_NAME="${SNS_TOPIC_NAME:-rds-alerts}"
ALARM_NAME="${RDS_INSTANCE_ID}-HighCPU"
THRESHOLD=80

echo "🔔 Creazione/aggiornamento SNS topic $SNS_TOPIC_NAME…"
TOPIC_ARN=$(aws sns create-topic --name "$SNS_TOPIC_NAME" \
            --query TopicArn --output text)

if [[ -n "${ALERT_EMAIL:-}" ]]; then
  echo "📧 Iscrivo $ALERT_EMAIL al topic (protocol email)…"
  aws sns subscribe --topic-arn "$TOPIC_ARN" \
    --protocol email --notification-endpoint "$ALERT_EMAIL" \
    --output text >/dev/null
fi

echo "📈 Configuro alarm CloudWatch $ALARM_NAME…"
aws cloudwatch put-metric-alarm \
  --alarm-name "$ALARM_NAME" \
  --metric-name CPUUtilization \
  --namespace AWS/RDS \
  --statistic Average \
  --period 300 \
  --threshold "$THRESHOLD" \
  --comparison-operator GreaterThanOrEqualToThreshold \
  --evaluation-periods 1 \
  --alarm-actions "$TOPIC_ARN" \
  --dimensions Name=DBInstanceIdentifier,Value="$RDS_INSTANCE_ID" \
  --unit Percent \
  --treat-missing-data notBreaching >/dev/null

echo "✅ Alarm $ALARM_NAME pronto (trigger > ${THRESHOLD}% CPU)."
