// 📄 src/app/api/auth/login/route.ts
// Endpoint Supabase login – Gestisce l'autenticazione degli utenti
// Utilizza Zod per la validazione e Supabase per l'autenticazione

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Inizializzazione client Supabase per server-side
const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Schema di validazione per i dati di login
const loginSchema = z.object({
  email: z.string().email('Email non valida'),
  password: z.string().min(6, 'Password deve essere di almeno 6 caratteri')
});

/**
 * Gestisce il login degli utenti
 * @param req - Request con email e password nel body
 * @returns Response con token di accesso o errore
 */
export async function POST(req: NextRequest) {
  try {
    // Parsing del body JSON
    let body: unknown;
    try {
      body = await req.json();
    } catch {
      return NextResponse.json({ error: 'JSON malformato' }, { status: 400 });
    }

    // Validazione dei dati di input
    const result = loginSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json({ 
        error: 'Validazione fallita', 
        details: result.error.errors 
      }, { status: 422 });
    }

    const { email, password } = result.data;

    // Tentativo di login con Supabase
    const { data, error } = await supabase.auth.signInWithPassword({ 
      email, 
      password 
    });

    if (error) {
      // Gestione errori specifici
      if (error.message && error.message.toLowerCase().includes('invalid')) {
        return NextResponse.json({ error: 'Credenziali non valide' }, { status: 401 });
      }
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Successo - ritorna i token e i dati utente
    return NextResponse.json({
      access_token: data.session?.access_token,
      refresh_token: data.session?.refresh_token,
      user: data.user
    }, { status: 200 });

  } catch (error) {
    console.error('Errore durante il login:', error);
    return NextResponse.json({ 
      error: 'Errore interno del server' 
    }, { status: 500 });
  }
}
