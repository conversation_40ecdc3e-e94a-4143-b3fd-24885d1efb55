# =============================================================================
# Database Configuration per ReBuild Link Infrastructure
# =============================================================================
# Questo file gestisce la creazione e configurazione dell'istanza RDS PostgreSQL
# con tutte le impostazioni di sicurezza, backup e performance ottimali.
# =============================================================================

# =============================================================================
# RDS PostgreSQL Instance
# =============================================================================
resource "aws_db_instance" "postgres" {
  # Identificatori e naming
  identifier     = var.db_instance_id
  db_name        = var.db_name
  username       = var.db_username
  password       = var.db_password

  # Engine configuration
  engine         = "postgres"
  engine_version = "15.8"  # Versione specifica per stabilità
  instance_class = "db.t3.micro"  # Free tier eligible

  # Storage configuration
  allocated_storage     = 20
  max_allocated_storage = 100  # Auto-scaling fino a 100GB
  storage_type          = "gp2"
  storage_encrypted     = true  # Crittografia a riposo

  # Network configuration
  publicly_accessible   = true
  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  db_subnet_group_name   = aws_db_subnet_group.rds_subnet_group.name

  # Backup configuration
  backup_retention_period = 7    # 7 giorni di backup
  backup_window          = "03:00-04:00"  # Backup alle 3 AM UTC
  copy_tags_to_snapshot  = true

  # Maintenance configuration
  maintenance_window         = "sun:04:00-sun:05:00"  # Domenica 4-5 AM UTC
  auto_minor_version_upgrade = true
  apply_immediately          = false  # Applica modifiche nella finestra di manutenzione

  # Performance configuration
  performance_insights_enabled = true
  monitoring_interval         = 60  # CloudWatch enhanced monitoring
  monitoring_role_arn        = aws_iam_role.rds_monitoring.arn

  # Deletion protection
  skip_final_snapshot       = false
  final_snapshot_identifier = "${var.db_instance_id}-final-snapshot-${formatdate("YYYY-MM-DD-hhmm", timestamp())}"
  deletion_protection       = false  # Set to true in production

  # Tags per gestione e billing
  tags = {
    Name         = "${var.db_instance_id}-postgresql"
    Database     = var.db_name
    Engine       = "PostgreSQL 15"
    Purpose      = "ReBuild Link Application Database"
    BackupPolicy = "7-days"
    Monitoring   = "Enhanced"
  }

  # Lifecycle management
  lifecycle {
    # Previeni distruzione accidentale
    prevent_destroy = false  # Set to true in production
    
    # Ignora cambiamenti di password (gestiti esternamente)
    ignore_changes = [
      password,
      final_snapshot_identifier
    ]
  }

  # Dipendenze esplicite
  depends_on = [
    aws_security_group.rds_sg,
    aws_db_subnet_group.rds_subnet_group,
    aws_iam_role.rds_monitoring
  ]
}

# =============================================================================
# IAM Role per Enhanced Monitoring
# =============================================================================
resource "aws_iam_role" "rds_monitoring" {
  name = "${var.db_instance_id}-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name    = "${var.db_instance_id} RDS Monitoring Role"
    Purpose = "Enhanced Monitoring for RDS"
  }
}

# Attach della policy AWS managed per RDS monitoring
resource "aws_iam_role_policy_attachment" "rds_monitoring" {
  role       = aws_iam_role.rds_monitoring.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# =============================================================================
# CloudWatch Alarms per Monitoring
# =============================================================================
resource "aws_cloudwatch_metric_alarm" "database_cpu" {
  alarm_name          = "${var.db_instance_id}-high-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors RDS CPU utilization"
  alarm_actions       = []  # Aggiungi SNS topic se necessario

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.postgres.id
  }

  tags = {
    Name    = "${var.db_instance_id} CPU Alarm"
    Purpose = "RDS Monitoring"
  }
}

resource "aws_cloudwatch_metric_alarm" "database_connections" {
  alarm_name          = "${var.db_instance_id}-high-connections"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "DatabaseConnections"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "15"  # 75% di 20 connessioni max per t3.micro
  alarm_description   = "This metric monitors RDS connection count"

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.postgres.id
  }

  tags = {
    Name    = "${var.db_instance_id} Connections Alarm"
    Purpose = "RDS Monitoring"
  }
}

# =============================================================================
# Outputs
# =============================================================================
output "rds_endpoint" {
  description = "Endpoint dell'istanza RDS"
  value       = aws_db_instance.postgres.endpoint
}

output "rds_address" {
  description = "Indirizzo dell'istanza RDS (senza porta)"
  value       = aws_db_instance.postgres.address
}

output "rds_port" {
  description = "Porta dell'istanza RDS"
  value       = aws_db_instance.postgres.port
}

output "rds_instance_id" {
  description = "ID dell'istanza RDS"
  value       = aws_db_instance.postgres.id
}

output "rds_arn" {
  description = "ARN dell'istanza RDS"
  value       = aws_db_instance.postgres.arn
}
