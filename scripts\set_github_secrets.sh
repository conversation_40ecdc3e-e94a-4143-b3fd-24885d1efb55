#!/usr/bin/env bash
# =============================================================
# Script: set_github_secrets.sh
# Corso : DevOps per Web App – lezione "Secrets & Config"
# Scopo  : Sal<PERSON>e in modo sicuro le variabili d'ambiente
#          (DATABASE_URL, SUPABASE_URL, SUPABASE_ANON_KEY)
#          nel repository GitHub come *Actions Secrets*.
# -------------------------------------------------------------
# Funzionamento:
#   1. Valida la presenza delle variabili richieste.
#   2. Identifica automaticamente il repo GitHub corrente con gh.
#   3. Crea/aggiorna i secrets tramite `gh secret set`.
#
# Prerequisiti:
#   • GitHub CLI (`gh`) autenticato: `gh auth login`
#   • Variabili d'ambiente esportate prima dell'esecuzione, es:
#
#       export DATABASE_URL="**********************************/db"
#       export SUPABASE_URL="https://proj.supabase.co"
#       export SUPABASE_ANON_KEY="eyJhbGci...superlong..."
#
# =============================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

echo "🔐 Configurazione GitHub Actions Secrets per ReBuild Link"
echo "========================================================="
echo ""

# -----------------------------
# 1. Verifica prerequisiti
# -----------------------------
echo "1️⃣  Verifica prerequisiti..."

# Controlla se GitHub CLI è installato
if ! command -v gh >/dev/null 2>&1; then
    echo "❌ GitHub CLI (gh) non trovato!"
    echo "   Installazione:"
    echo "   • macOS: brew install gh"
    echo "   • Ubuntu: sudo apt install gh"
    echo "   • Windows: winget install GitHub.cli"
    echo ""
    echo "   Dopo l'installazione, autentica con: gh auth login"
    exit 1
fi

# Controlla se GitHub CLI è autenticato
if ! gh auth status >/dev/null 2>&1; then
    echo "❌ GitHub CLI non autenticato!"
    echo "   Esegui: gh auth login"
    echo "   Segui le istruzioni per autenticarti con il tuo account GitHub."
    exit 1
fi

echo "   ✅ GitHub CLI installato e autenticato"

# -----------------------------
# 2. Verifica variabili d'ambiente
# -----------------------------
echo ""
echo "2️⃣  Verifica variabili d'ambiente..."

# Lista delle variabili richieste per il deployment
REQUIRED_VARS=(
    "DATABASE_URL"
    "SUPABASE_URL" 
    "SUPABASE_ANON_KEY"
)

# Array per tenere traccia delle variabili mancanti
MISSING_VARS=()

# Controlla ogni variabile richiesta
for var in "${REQUIRED_VARS[@]}"; do
    if [[ -z "${!var:-}" ]]; then
        echo "   ❌ $var non impostata"
        MISSING_VARS+=("$var")
    else
        # Mostra solo i primi caratteri per sicurezza
        value="${!var}"
        preview="${value:0:20}..."
        echo "   ✅ $var impostata ($preview)"
    fi
done

# Se mancano variabili, mostra istruzioni e esci
if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo ""
    echo "❌ Variabili mancanti: ${MISSING_VARS[*]}"
    echo ""
    echo "💡 Esporta le variabili mancanti prima di eseguire questo script:"
    echo ""
    for var in "${MISSING_VARS[@]}"; do
        case "$var" in
            "DATABASE_URL")
                echo "   export DATABASE_URL=\"**************************************/database\""
                echo "   # Ottieni questo valore dopo aver creato l'istanza RDS"
                ;;
            "SUPABASE_URL")
                echo "   export SUPABASE_URL=\"https://your-project.supabase.co\""
                echo "   # Dalla dashboard Supabase > Settings > API"
                ;;
            "SUPABASE_ANON_KEY")
                echo "   export SUPABASE_ANON_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\""
                echo "   # Dalla dashboard Supabase > Settings > API > anon public"
                ;;
        esac
        echo ""
    done
    exit 1
fi

# -----------------------------
# 3. Identifica repository GitHub
# -----------------------------
echo ""
echo "3️⃣  Identifica repository GitHub..."

# Ottieni il nome del repository corrente
REPO="${REPO:-$(gh repo view --json nameWithOwner -q .nameWithOwner 2>/dev/null || echo "")}"

if [[ -z "$REPO" ]]; then
    echo "❌ Non riesco a identificare il repository GitHub!"
    echo "   Assicurati di essere in una cartella con un repository Git collegato a GitHub."
    echo "   Oppure specifica manualmente: export REPO=\"username/repository-name\""
    exit 1
fi

echo "   📒 Repository: $REPO"

# Verifica che abbiamo i permessi per modificare i secrets
if ! gh secret list --repo "$REPO" >/dev/null 2>&1; then
    echo "❌ Non hai i permessi per gestire i secrets del repository $REPO"
    echo "   Assicurati di essere un collaboratore con permessi di scrittura."
    exit 1
fi

# -----------------------------
# 4. Salva i secrets su GitHub
# -----------------------------
echo ""
echo "4️⃣  Salvataggio secrets su GitHub..."

# Salva ogni variabile come secret GitHub
for var in "${REQUIRED_VARS[@]}"; do
    echo "   🔐 Salvando $var..."
    
    # Usa gh secret set per salvare il secret in modo sicuro
    if echo "${!var}" | gh secret set "$var" --repo "$REPO"; then
        echo "   ✅ $var salvato con successo"
    else
        echo "   ❌ Errore nel salvare $var"
        exit 1
    fi
done

# -----------------------------
# 5. Verifica finale
# -----------------------------
echo ""
echo "5️⃣  Verifica finale..."

echo "   📋 Secrets attualmente configurati:"
gh secret list --repo "$REPO" | grep -E "(DATABASE_URL|SUPABASE_URL|SUPABASE_ANON_KEY)" || echo "   ⚠️  Nessun secret trovato (potrebbe essere un problema di permessi)"

# -----------------------------
# 6. Riepilogo e prossimi passi
# -----------------------------
echo ""
echo "🎉 Configurazione completata con successo!"
echo ""
echo "📋 Riepilogo:"
echo "   • Repository: $REPO"
echo "   • Secrets configurati: ${#REQUIRED_VARS[@]}"
echo "   • Pronti per GitHub Actions CI/CD"
echo ""
echo "🚀 Prossimi passi:"
echo "   1. I secrets sono ora disponibili nei workflow GitHub Actions"
echo "   2. Configura le stesse variabili su Vercel per il deployment"
echo "   3. Testa il deployment con un push al repository"
echo ""
echo "💡 Per vedere i secrets: gh secret list --repo $REPO"
echo "💡 Per rimuovere un secret: gh secret delete SECRET_NAME --repo $REPO"
