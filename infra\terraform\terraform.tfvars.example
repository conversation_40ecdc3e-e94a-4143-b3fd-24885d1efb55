# =============================================================================
# Terraform Variables Example per ReBuild Link Infrastructure
# =============================================================================
# Questo file contiene esempi di tutte le variabili necessarie per
# il deployment dell'infrastruttura. Copia questo file in terraform.tfvars
# e personalizza i valori con le tue configurazioni reali.
# 
# IMPORTANTE: Non committare mai terraform.tfvars nel repository!
# =============================================================================

# =============================================================================
# AWS Configuration
# =============================================================================

# Regione AWS dove creare le risorse
aws_region = "eu-central-1"

# Configurazione database RDS PostgreSQL
db_instance_id = "rebuildlink-db"
db_name        = "rebuildlink"
db_username    = "rebuild_user"
db_password    = "SuperSegreta123!"  # CAMBIA CON UNA PASSWORD SICURA!

# CIDR del tuo IP pubblico per accesso al database
# Ottieni il tuo IP: curl ifconfig.me
# Formato: "*******/32" per IP singolo o "***********/24" per subnet
allowed_cidr = "*******/32"  # SOSTITUISCI CON IL TUO IP PUBBLICO

# =============================================================================
# GitHub Configuration
# =============================================================================

# Token di accesso personale GitHub (PAT)
# Crea su: https://github.com/settings/tokens
# Scope richiesti: repo, admin:repo_hook
github_token = "ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # SOSTITUISCI

# Proprietario del repository (username o organizzazione)
github_owner = "tuo-username"  # SOSTITUISCI

# Nome del repository (senza owner/)
github_repo = "rebuild-link"  # SOSTITUISCI

# =============================================================================
# Application Secrets
# =============================================================================

# URL completo di connessione al database
# Verrà generato automaticamente, ma puoi personalizzarlo se necessario
# Formato: postgres://username:password@host:port/database
database_url = "*******************************************************/rebuildlink"

# URL del progetto Supabase
# Ottieni da: https://app.supabase.com/project/[project-id]/settings/api
supabase_url = "https://xxxxxxxxxxxxxxxxx.supabase.co"  # SOSTITUISCI

# Chiave anonima Supabase (pubblica)
# Ottieni da: https://app.supabase.com/project/[project-id]/settings/api
supabase_anon_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # SOSTITUISCI

# =============================================================================
# Vercel Configuration
# =============================================================================

# Token di accesso Vercel
# Crea su: https://vercel.com/account/tokens
vercel_token = "vercel_pat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"  # SOSTITUISCI

# ID del progetto Vercel
# Ottieni da: https://vercel.com/[team]/[project]/settings
# Formato: prj_xxxxxxxxxxxxxxxxxxxxxxxx
vercel_project_id = "prj_xxxxxxxxxxxxxxxxxxxxxxxx"  # SOSTITUISCI

# =============================================================================
# Istruzioni per ottenere i valori
# =============================================================================

# 1. IP PUBBLICO:
#    curl ifconfig.me
#    Aggiungi /32 alla fine (es: *******/32)

# 2. GITHUB TOKEN:
#    - Vai su https://github.com/settings/tokens
#    - Clicca "Generate new token (classic)"
#    - Seleziona scope: repo, admin:repo_hook
#    - Copia il token generato

# 3. SUPABASE CREDENTIALS:
#    - Vai su https://app.supabase.com
#    - Seleziona il tuo progetto
#    - Settings > API
#    - Copia URL e anon key

# 4. VERCEL TOKEN:
#    - Vai su https://vercel.com/account/tokens
#    - Crea nuovo token
#    - Copia il token generato

# 5. VERCEL PROJECT ID:
#    - Vai su https://vercel.com
#    - Seleziona il tuo progetto
#    - Settings > General
#    - Copia Project ID

# =============================================================================
# Esempio di utilizzo
# =============================================================================

# 1. Copia questo file:
#    cp terraform.tfvars.example terraform.tfvars

# 2. Modifica terraform.tfvars con i tuoi valori reali

# 3. Inizializza Terraform:
#    terraform init

# 4. Pianifica il deployment:
#    terraform plan

# 5. Applica le modifiche:
#    terraform apply

# 6. Per distruggere l'infrastruttura:
#    terraform destroy

# =============================================================================
# Note di sicurezza
# =============================================================================

# ⚠️  IMPORTANTE:
# - Non committare mai terraform.tfvars nel repository
# - Usa password complesse per il database
# - Limita allowed_cidr al tuo IP specifico
# - Ruota periodicamente i token di accesso
# - Abilita 2FA su tutti gli account (GitHub, Vercel, AWS)
