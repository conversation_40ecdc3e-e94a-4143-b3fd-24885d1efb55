-- ============================================================
-- Migration: 002_add_sample_data.sql
-- Data: 2025-01-09
-- Scopo: Dati di esempio per sviluppo e testing
-- Autore: ReBuild Link Team
-- ------------------------------------------------------------
-- Questa migrazione aggiunge dati di esempio per facilitare
-- lo sviluppo e il testing dell'applicazione.
--
-- Include:
--   • Tenants di esempio (ua, it)
--   • Categorie base
--   • Utenti demo
--   • Annunci di esempio
-- ============================================================

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 1. TENANTS - Configurazione multi-tenant
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

INSERT INTO tenants (id, slug, name, default_locale, is_active) VALUES
    (1, 'ua', 'Ukraine Reconstruction', 'ua', true),
    (2, 'it', 'Italia Ricostruzione', 'it', true)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    default_locale = EXCLUDED.default_locale,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 2. CATEGORIES - Categorie base per classificazione
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

INSERT INTO categories (id, name, description, icon, is_active) VALUES
    (1, 'Materials', 'Construction materials and supplies', 'hammer', true),
    (2, 'Labor', 'Skilled and unskilled labor services', 'users', true),
    (3, 'Equipment', 'Heavy machinery and construction equipment', 'truck', true),
    (4, 'Services', 'Professional and consulting services', 'briefcase', true),
    (5, 'Logistics', 'Transportation and delivery services', 'shipping-fast', true)
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    icon = EXCLUDED.icon,
    is_active = EXCLUDED.is_active;

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 3. USERS - Utenti demo per testing
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

-- Utenti per tenant Ukraine (ua)
INSERT INTO users (
    id, tenant_id, email, user_type, display_name, company_name, 
    phone, location, bio, is_verified, is_active
) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        1,
        '<EMAIL>',
        'company',
        'Kyiv Construction Ltd',
        'Kyiv Construction Ltd',
        '+380501234567',
        'Kyiv, Ukraine',
        'Leading construction company specializing in post-war reconstruction projects.',
        true,
        true
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        1,
        '<EMAIL>',
        'individual',
        'Oleksandr Petrenko',
        null,
        '+380509876543',
        'Kharkiv, Ukraine',
        'Experienced mason and construction worker available for reconstruction projects.',
        true,
        true
    )
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    user_type = EXCLUDED.user_type,
    display_name = EXCLUDED.display_name,
    company_name = EXCLUDED.company_name,
    phone = EXCLUDED.phone,
    location = EXCLUDED.location,
    bio = EXCLUDED.bio,
    is_verified = EXCLUDED.is_verified,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- Utenti per tenant Italia (it)
INSERT INTO users (
    id, tenant_id, email, user_type, display_name, company_name, 
    phone, location, bio, is_verified, is_active
) VALUES
    (
        '00000000-0000-0000-0000-000000000003',
        2,
        '<EMAIL>',
        'company',
        'Materiali Edili Roma SRL',
        'Materiali Edili Roma SRL',
        '+390612345678',
        'Roma, Italia',
        'Fornitore specializzato in materiali edili per grandi progetti di ricostruzione.',
        true,
        true
    )
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    user_type = EXCLUDED.user_type,
    display_name = EXCLUDED.display_name,
    company_name = EXCLUDED.company_name,
    phone = EXCLUDED.phone,
    location = EXCLUDED.location,
    bio = EXCLUDED.bio,
    is_verified = EXCLUDED.is_verified,
    is_active = EXCLUDED.is_active,
    updated_at = CURRENT_TIMESTAMP;

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 4. ANNOUNCEMENTS - Annunci di esempio
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

-- Annunci per tenant Ukraine (ua)
INSERT INTO announcements (
    id, tenant_id, creator_id, type, title, description, region, location,
    budget_min, budget_max, salary_range, availability, status, priority
) VALUES
    (
        1,
        1,
        '00000000-0000-0000-0000-000000000001',
        'project',
        'Acquisto 500 tonnellate di cemento',
        'Richiesta preventivo per fornitura di cemento Portland per progetto di ricostruzione residenziale. Consegna richiesta entro 30 giorni.',
        'Kyiv Oblast',
        'Kyiv, Ukraine',
        10000.00,
        25000.00,
        null,
        null,
        'active',
        1
    ),
    (
        2,
        1,
        '00000000-0000-0000-0000-000000000002',
        'job',
        'Muratore esperto disponibile',
        'Muratore con 15 anni di esperienza disponibile per progetti di ricostruzione. Specializzato in lavori di restauro e riparazione.',
        'Kharkiv Oblast',
        'Kharkiv, Ukraine',
        null,
        null,
        '€1500-€1800/mese',
        'full-time',
        'active',
        0
    )
ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    region = EXCLUDED.region,
    location = EXCLUDED.location,
    budget_min = EXCLUDED.budget_min,
    budget_max = EXCLUDED.budget_max,
    salary_range = EXCLUDED.salary_range,
    availability = EXCLUDED.availability,
    status = EXCLUDED.status,
    priority = EXCLUDED.priority,
    updated_at = CURRENT_TIMESTAMP;

-- Annunci per tenant Italia (it)
INSERT INTO announcements (
    id, tenant_id, creator_id, type, title, description, region, location,
    budget_min, budget_max, salary_range, availability, status, priority
) VALUES
    (
        3,
        2,
        '00000000-0000-0000-0000-000000000003',
        'material',
        'Fornitura laterizi per ricostruzione',
        'Disponibili 20.000 mattoni di alta qualità per progetti di ricostruzione. Consegna in tutta Italia.',
        'Lazio',
        'Roma, Italia',
        5000.00,
        8000.00,
        null,
        null,
        'active',
        1
    )
ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    region = EXCLUDED.region,
    location = EXCLUDED.location,
    budget_min = EXCLUDED.budget_min,
    budget_max = EXCLUDED.budget_max,
    salary_range = EXCLUDED.salary_range,
    availability = EXCLUDED.availability,
    status = EXCLUDED.status,
    priority = EXCLUDED.priority,
    updated_at = CURRENT_TIMESTAMP;

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 5. ANNOUNCEMENT_CATEGORIES - Associazioni categorie-annunci
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

INSERT INTO announcement_categories (announcement_id, category_id) VALUES
    (1, 1),  -- Cemento → Materials
    (2, 2),  -- Muratore → Labor
    (3, 1)   -- Laterizi → Materials
ON CONFLICT (announcement_id, category_id) DO NOTHING;

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 6. EVENT_LOGS - Log eventi di esempio
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

INSERT INTO event_logs (
    tenant_id, user_id, event_type, entity_type, entity_id, 
    metadata, ip_address, user_agent
) VALUES
    (
        1,
        '00000000-0000-0000-0000-000000000001',
        'announcement_created',
        'announcement',
        1,
        '{"type": "project", "budget_range": "10000-25000", "category": "materials"}',
        '*************',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ),
    (
        1,
        '00000000-0000-0000-0000-000000000002',
        'announcement_created',
        'announcement',
        2,
        '{"type": "job", "availability": "full-time", "category": "labor"}',
        '*************',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    ),
    (
        2,
        '00000000-0000-0000-0000-000000000003',
        'announcement_created',
        'announcement',
        3,
        '{"type": "material", "budget_range": "5000-8000", "category": "materials"}',
        '*************',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    );

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 7. COMMENTI FINALI
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

-- Dati di esempio caricati con successo!
-- Questo include:
--   ✅ 2 tenants (ua, it)
--   ✅ 5 categorie base
--   ✅ 3 utenti demo
--   ✅ 3 annunci di esempio
--   ✅ Associazioni categorie
--   ✅ Log eventi di esempio
--
-- L'applicazione è ora pronta per il testing con dati realistici!
