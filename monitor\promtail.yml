# =============================================================================
# Promtail Configuration per ReBuild Link Log Aggregation
# =============================================================================
# Configurazione Promtail per raccogliere e inviare log di sistema
# e applicazione a Grafana Loki per analisi centralizzata.
# =============================================================================

# =============================================================================
# Server Configuration
# =============================================================================
server:
  http_listen_port: 9080
  grpc_listen_port: 0
  log_level: info

# =============================================================================
# Positions File - Tracking dei file letti
# =============================================================================
positions:
  filename: /tmp/positions/positions.yaml
  sync_period: 10s
  ignore_invalid_yaml: false

# =============================================================================
# Client Configuration - Destinazione log
# =============================================================================
clients:
  # Configurazione per Grafana Loki (se disponibile)
  - url: http://grafana:3100/loki/api/v1/push
    timeout: 10s
    backoff_config:
      min_period: 500ms
      max_period: 5m
      max_retries: 10
    
    # Headers personalizzati
    headers:
      X-Scope-OrgID: "rebuild-link"

# =============================================================================
# Scrape Configurations - Definizione sorgenti log
# =============================================================================
scrape_configs:
  
  # ---------------------------------------------------------------------------
  # System Logs - Log di sistema generale
  # ---------------------------------------------------------------------------
  - job_name: system-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: system
          environment: local
          service: rebuild-link
          __path__: /var/log/*.log
    
    # Pipeline per parsing log di sistema
    pipeline_stages:
      # Estrai timestamp se presente
      - regex:
          expression: '^(?P<timestamp>\w+\s+\d+\s+\d+:\d+:\d+)\s+(?P<hostname>\S+)\s+(?P<service>\S+):\s+(?P<message>.*)'
      
      # Converti timestamp
      - timestamp:
          source: timestamp
          format: "Jan 02 15:04:05"
          fallback_formats:
            - "2006-01-02T15:04:05Z"
            - "2006-01-02 15:04:05"
      
      # Aggiungi labels dinamici
      - labels:
          hostname: hostname
          service: service

  # ---------------------------------------------------------------------------
  # Application Logs - Log specifici dell'applicazione
  # ---------------------------------------------------------------------------
  - job_name: application-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: application
          environment: local
          service: rebuild-link
          component: nextjs
          __path__: /var/log/rebuild-link/*.log
    
    # Pipeline per parsing log applicazione
    pipeline_stages:
      # Parsing log JSON (se l'app logga in JSON)
      - json:
          expressions:
            level: level
            message: message
            timestamp: timestamp
            component: component
      
      # Timestamp parsing
      - timestamp:
          source: timestamp
          format: RFC3339
      
      # Labels dinamici
      - labels:
          level: level
          component: component

  # ---------------------------------------------------------------------------
  # Docker Container Logs - Log dei container Docker
  # ---------------------------------------------------------------------------
  - job_name: docker-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          environment: local
          __path__: /var/lib/docker/containers/*/*.log
    
    # Pipeline per parsing log Docker
    pipeline_stages:
      # Parsing formato log Docker
      - json:
          expressions:
            log: log
            stream: stream
            time: time
      
      # Timestamp Docker
      - timestamp:
          source: time
          format: RFC3339Nano
      
      # Estrai container ID dal path
      - regex:
          source: filename
          expression: '/var/lib/docker/containers/(?P<container_id>[^/]+)/.*\.log'
      
      # Labels per container
      - labels:
          container_id: container_id
          stream: stream

  # ---------------------------------------------------------------------------
  # PostgreSQL Logs - Log del database (se accessibili)
  # ---------------------------------------------------------------------------
  - job_name: postgresql-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgresql
          environment: local
          service: database
          __path__: /var/log/postgresql/*.log
    
    # Pipeline per parsing log PostgreSQL
    pipeline_stages:
      # Parsing log PostgreSQL
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\.\d+\s+\w+)\s+\[(?P<pid>\d+)\]\s+(?P<level>\w+):\s+(?P<message>.*)'
      
      # Timestamp PostgreSQL
      - timestamp:
          source: timestamp
          format: "2006-01-02 15:04:05.000 MST"
      
      # Labels PostgreSQL
      - labels:
          pid: pid
          level: level

  # ---------------------------------------------------------------------------
  # Nginx/Apache Logs - Web server logs (se presenti)
  # ---------------------------------------------------------------------------
  - job_name: webserver-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: webserver
          environment: local
          service: nginx
          __path__: /var/log/nginx/*.log
    
    # Pipeline per parsing access log
    pipeline_stages:
      # Parsing access log format
      - regex:
          expression: '^(?P<remote_addr>\S+)\s+\S+\s+\S+\s+\[(?P<timestamp>[^\]]+)\]\s+"(?P<method>\S+)\s+(?P<path>\S+)\s+(?P<protocol>\S+)"\s+(?P<status>\d+)\s+(?P<bytes>\d+)\s+"(?P<referer>[^"]*)"\s+"(?P<user_agent>[^"]*)"'
      
      # Timestamp access log
      - timestamp:
          source: timestamp
          format: "02/Jan/2006:15:04:05 -0700"
      
      # Labels per web server
      - labels:
          method: method
          status: status
          remote_addr: remote_addr

# =============================================================================
# Limits Configuration - Limiti per performance
# =============================================================================
limits_config:
  # Limite readline per file grandi
  readline_rate: 10000
  readline_rate_burst: 20000
  
  # Limite dimensione stream
  max_streams: 10000
  max_line_size: 256000
  max_line_size_truncate: true

# =============================================================================
# Target Configuration - Configurazione target discovery
# =============================================================================
target_config:
  sync_period: 10s
