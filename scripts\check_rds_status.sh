#!/usr/bin/env bash
# ============================================================
# Script: check_rds_status.sh
# Scopo : Controlla lo stato di un'istanza Amazon RDS
# Autore: ReBuild Link Team
# ------------------------------------------------------------
# <PERSON>o script ti aiuta a monitorare lo stato della tua
# istanza RDS e ottenere le informazioni di connessione
# quando è pronta.
#
# 👉 Prerequisiti:
#    • AWS CLI configurata (`aws configure`)
#
# ============================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

# -------------------------------
# 1. Parametri personalizzabili
# -------------------------------
DB_INSTANCE_ID="${DB_INSTANCE_ID:-rebuildlink-db}"  # Nome istanza RDS
AWS_REGION="${AWS_REGION:-eu-central-1}"            # Regione AWS

# -------------------------------
# 2. Controlla se l'istanza esiste
# -------------------------------
echo "🔍 Controllo stato dell'istanza RDS: $DB_INSTANCE_ID"
echo ""

# Verifica se l'istanza esiste
if ! aws rds describe-db-instances --db-instance-identifier "$DB_INSTANCE_ID" --region "$AWS_REGION" >/dev/null 2>&1; then
    echo "❌ Istanza $DB_INSTANCE_ID non trovata nella regione $AWS_REGION"
    echo "   Verifica che il nome sia corretto o che l'istanza sia stata creata."
    exit 1
fi

# -------------------------------
# 3. Ottieni informazioni dettagliate
# -------------------------------
echo "📊 Informazioni istanza:"

# Ottieni tutte le informazioni in una sola chiamata
DB_INFO=$(aws rds describe-db-instances \
            --db-instance-identifier "$DB_INSTANCE_ID" \
            --region "$AWS_REGION" \
            --query 'DBInstances[0]')

# Estrai i campi necessari
STATUS=$(echo "$DB_INFO" | jq -r '.DBInstanceStatus')
ENDPOINT=$(echo "$DB_INFO" | jq -r '.Endpoint.Address // "N/A"')
PORT=$(echo "$DB_INFO" | jq -r '.Endpoint.Port // "N/A"')
DB_NAME=$(echo "$DB_INFO" | jq -r '.DBName // "N/A"')
USERNAME=$(echo "$DB_INFO" | jq -r '.MasterUsername // "N/A"')
ENGINE=$(echo "$DB_INFO" | jq -r '.Engine')
ENGINE_VERSION=$(echo "$DB_INFO" | jq -r '.EngineVersion')
INSTANCE_CLASS=$(echo "$DB_INFO" | jq -r '.DBInstanceClass')
STORAGE=$(echo "$DB_INFO" | jq -r '.AllocatedStorage')

echo "   Status: $STATUS"
echo "   Engine: $ENGINE $ENGINE_VERSION"
echo "   Instance Class: $INSTANCE_CLASS"
echo "   Storage: ${STORAGE}GB"
echo "   Database Name: $DB_NAME"
echo "   Master User: $USERNAME"

if [[ "$ENDPOINT" != "N/A" ]]; then
    echo "   Endpoint: $ENDPOINT:$PORT"
fi

echo ""

# -------------------------------
# 4. Azioni basate sullo stato
# -------------------------------
case "$STATUS" in
    "available")
        echo "✅ Istanza DISPONIBILE!"
        echo ""
        echo "🔗 Informazioni di connessione:"
        echo "   Host: $ENDPOINT"
        echo "   Port: $PORT"
        echo "   Database: $DB_NAME"
        echo "   Username: $USERNAME"
        echo ""
        echo "📝 DATABASE_URL di esempio:"
        echo "   postgres://$USERNAME:<PASSWORD>@$ENDPOINT:$PORT/$DB_NAME"
        echo ""
        echo "💡 Sostituisci <PASSWORD> con la password che hai impostato durante la creazione."
        ;;
    "creating")
        echo "⏳ Istanza in fase di CREAZIONE..."
        echo "   Questo processo può richiedere 10-15 minuti."
        echo "   Riprova tra qualche minuto."
        ;;
    "backing-up")
        echo "💾 Istanza in fase di BACKUP..."
        echo "   L'istanza è disponibile ma potrebbe essere più lenta."
        ;;
    "modifying")
        echo "🔧 Istanza in fase di MODIFICA..."
        echo "   Attendere il completamento delle modifiche."
        ;;
    "deleting")
        echo "🗑️  Istanza in fase di ELIMINAZIONE..."
        echo "   L'istanza sarà presto rimossa."
        ;;
    *)
        echo "⚠️  Stato sconosciuto: $STATUS"
        echo "   Controlla la console AWS per maggiori dettagli."
        ;;
esac

echo ""
echo "🔄 Per monitorare in tempo reale:"
echo "   watch -n 30 './scripts/check_rds_status.sh'"
echo ""
echo "📱 Console AWS:"
echo "   https://console.aws.amazon.com/rds/home?region=$AWS_REGION#database:id=$DB_INSTANCE_ID"
