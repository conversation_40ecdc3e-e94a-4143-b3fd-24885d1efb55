# =============================================================================
# Variables Configuration per ReBuild Link Infrastructure
# =============================================================================
# Questo file definisce tutte le variabili utilizzate nei moduli Terraform
# per configurare l'infrastruttura AWS, GitHub e Vercel.
# =============================================================================

# =============================================================================
# AWS Configuration Variables
# =============================================================================

variable "aws_region" {
  description = "Regione AWS dove creare le risorse"
  type        = string
  default     = "eu-central-1"
  
  validation {
    condition = can(regex("^[a-z]{2}-[a-z]+-[0-9]$", var.aws_region))
    error_message = "La regione AWS deve essere nel formato corretto (es: eu-central-1)."
  }
}

variable "db_instance_id" {
  description = "Identificatore univoco per l'istanza RDS"
  type        = string
  default     = "rebuildlink-db"
  
  validation {
    condition = can(regex("^[a-z][a-z0-9-]*[a-z0-9]$", var.db_instance_id))
    error_message = "L'ID istanza deve iniziare con una lettera e contenere solo lettere minuscole, numeri e trattini."
  }
}

variable "db_name" {
  description = "Nome del database PostgreSQL"
  type        = string
  default     = "rebuildlink"
  
  validation {
    condition = can(regex("^[a-zA-Z][a-zA-Z0-9_]*$", var.db_name))
    error_message = "Il nome database deve iniziare con una lettera e contenere solo lettere, numeri e underscore."
  }
}

variable "db_username" {
  description = "Username per l'accesso al database"
  type        = string
  default     = "rebuild_user"
  
  validation {
    condition = length(var.db_username) >= 3 && length(var.db_username) <= 16
    error_message = "L'username deve essere tra 3 e 16 caratteri."
  }
}

variable "db_password" {
  description = "Password per l'accesso al database (sensibile)"
  type        = string
  sensitive   = true
  
  validation {
    condition = length(var.db_password) >= 8
    error_message = "La password deve essere di almeno 8 caratteri."
  }
}

variable "allowed_cidr" {
  description = "CIDR block da cui permettere connessioni al database (es: *******/32 per IP specifico)"
  type        = string
  
  validation {
    condition = can(cidrhost(var.allowed_cidr, 0))
    error_message = "Il CIDR deve essere nel formato valido (es: ***********/24 o *******/32)."
  }
}

# =============================================================================
# GitHub Configuration Variables
# =============================================================================

variable "github_token" {
  description = "Token di accesso personale GitHub (PAT) con scope repo"
  type        = string
  sensitive   = true
  
  validation {
    condition = can(regex("^(ghp_|github_pat_)", var.github_token))
    error_message = "Il token GitHub deve iniziare con 'ghp_' o 'github_pat_'."
  }
}

variable "github_owner" {
  description = "Username o organizzazione GitHub proprietaria del repository"
  type        = string
  
  validation {
    condition = length(var.github_owner) > 0
    error_message = "Il proprietario GitHub non può essere vuoto."
  }
}

variable "github_repo" {
  description = "Nome del repository GitHub (senza owner/)"
  type        = string
  
  validation {
    condition = can(regex("^[a-zA-Z0-9._-]+$", var.github_repo))
    error_message = "Il nome repository può contenere solo lettere, numeri, punti, underscore e trattini."
  }
}

# =============================================================================
# Application Secrets Variables
# =============================================================================

variable "database_url" {
  description = "URL completo di connessione al database PostgreSQL"
  type        = string
  sensitive   = true
  
  validation {
    condition = can(regex("^postgres(ql)?://", var.database_url))
    error_message = "La DATABASE_URL deve iniziare con 'postgres://' o 'postgresql://'."
  }
}

variable "supabase_url" {
  description = "URL del progetto Supabase"
  type        = string
  sensitive   = true
  
  validation {
    condition = can(regex("^https://.*\\.supabase\\.co$", var.supabase_url))
    error_message = "L'URL Supabase deve essere nel formato https://xxx.supabase.co."
  }
}

variable "supabase_anon_key" {
  description = "Chiave anonima Supabase per autenticazione client-side"
  type        = string
  sensitive   = true
  
  validation {
    condition = can(regex("^eyJ", var.supabase_anon_key))
    error_message = "La chiave Supabase deve essere un JWT valido (inizia con 'eyJ')."
  }
}

# =============================================================================
# Vercel Configuration Variables
# =============================================================================

variable "vercel_token" {
  description = "Token di accesso Vercel per gestire progetti e environment variables"
  type        = string
  sensitive   = true
  
  validation {
    condition = can(regex("^vercel_", var.vercel_token))
    error_message = "Il token Vercel deve iniziare con 'vercel_'."
  }
}

variable "vercel_project_id" {
  description = "ID del progetto Vercel (formato prj_xxxxx)"
  type        = string
  
  validation {
    condition = can(regex("^prj_", var.vercel_project_id))
    error_message = "L'ID progetto Vercel deve iniziare con 'prj_'."
  }
}
