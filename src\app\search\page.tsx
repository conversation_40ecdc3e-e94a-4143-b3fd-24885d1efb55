/**
 * Pagina di ricerca - mostra i risultati della ricerca
 */
export default function SearchPage({
  searchParams,
}: {
  searchParams: { query?: string; cat?: string; loc?: string };
}) {
  const { query, cat, loc } = searchParams;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Risultati della ricerca</h1>
      
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="font-semibold mb-2">Parametri di ricerca:</h2>
        <ul className="space-y-1 text-sm">
          <li><strong>Query:</strong> {query || 'Nessuna'}</li>
          <li><strong>Categoria:</strong> {cat || 'Tutte'}</li>
          <li><strong>Località:</strong> {loc || 'Ovunque'}</li>
        </ul>
      </div>

      <div className="text-center py-12">
        <p className="text-gray-500">
          Funzionalità di ricerca implementata! I parametri vengono passati correttamente.
        </p>
      </div>
    </div>
  );
}
