# =============================================================================
# Docker Compose per Monitoring Stack ReBuild Link
# =============================================================================
# Stack di monitoring locale con Grafana e Promtail per analisi
# performance PostgreSQL e log aggregation.
# 
# Utilizzo:
#   docker compose up -d     # Avvia in background
#   docker compose logs -f   # Visualizza log
#   docker compose down      # Ferma e rimuove container
# =============================================================================

version: "3.9"

services:
  # ===========================================================================
  # Grafana - Dashboard e visualizzazione metriche
  # ===========================================================================
  grafana:
    image: grafana/grafana:10.4.1
    container_name: rebuild-link-grafana
    restart: unless-stopped
    
    # Porte esposte
    ports:
      - "3001:3000"  # Grafana UI su http://localhost:3001
    
    # Volumi per persistenza e configurazione
    volumes:
      # Configurazione dashboard automatica
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      
      # Persistenza dati Grafana (opzionale)
      - grafana-storage:/var/lib/grafana
    
    # Variabili d'ambiente
    environment:
      # Credenziali admin di default
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      
      # Configurazione generale
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_USERS_ALLOW_ORG_CREATE=false
      - GF_USERS_AUTO_ASSIGN_ORG=true
      - GF_USERS_AUTO_ASSIGN_ORG_ROLE=Viewer
      
      # Configurazione dashboard
      - GF_DASHBOARDS_DEFAULT_HOME_DASHBOARD_PATH=/etc/grafana/provisioning/dashboards/pg_stat_dashboard.json
      
      # Disabilita analytics
      - GF_ANALYTICS_REPORTING_ENABLED=false
      - GF_ANALYTICS_CHECK_FOR_UPDATES=false
      
      # Configurazione logging
      - GF_LOG_LEVEL=info
      - GF_LOG_MODE=console
    
    # Healthcheck
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Labels per identificazione
    labels:
      - "com.rebuild-link.service=monitoring"
      - "com.rebuild-link.component=grafana"
    
    # Network personalizzato
    networks:
      - monitoring

  # ===========================================================================
  # Promtail - Log aggregation e forwarding
  # ===========================================================================
  promtail:
    image: grafana/promtail:2.9.3
    container_name: rebuild-link-promtail
    restart: unless-stopped
    
    # Comando personalizzato con config file
    command: -config.file=/etc/promtail/config.yml
    
    # Volumi per accesso log e configurazione
    volumes:
      # Configurazione Promtail
      - ./promtail.yml:/etc/promtail/config.yml:ro
      
      # Accesso ai log di sistema (read-only)
      - /var/log:/var/log:ro
      
      # Log Docker (se disponibili)
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      
      # Posizioni file per tracking
      - promtail-positions:/tmp/positions
    
    # Variabili d'ambiente
    environment:
      - HOSTNAME=${HOSTNAME:-localhost}
    
    # Dipendenze
    depends_on:
      grafana:
        condition: service_healthy
    
    # Labels per identificazione
    labels:
      - "com.rebuild-link.service=monitoring"
      - "com.rebuild-link.component=promtail"
    
    # Network personalizzato
    networks:
      - monitoring

  # ===========================================================================
  # PostgreSQL Exporter (opzionale - per metriche avanzate)
  # ===========================================================================
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.15.0
    container_name: rebuild-link-postgres-exporter
    restart: unless-stopped
    
    # Porte esposte
    ports:
      - "9187:9187"  # Metriche Prometheus
    
    # Variabili d'ambiente per connessione PostgreSQL
    environment:
      # Configurazione connessione database
      # IMPORTANTE: Sostituisci con i tuoi valori reali
      - DATA_SOURCE_NAME=*********************************************************/rebuildlink?sslmode=require
      
      # Configurazione exporter
      - PG_EXPORTER_WEB_LISTEN_ADDRESS=:9187
      - PG_EXPORTER_WEB_TELEMETRY_PATH=/metrics
      - PG_EXPORTER_DISABLE_DEFAULT_METRICS=false
      - PG_EXPORTER_DISABLE_SETTINGS_METRICS=false
    
    # Healthcheck
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:9187/metrics || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    # Labels per identificazione
    labels:
      - "com.rebuild-link.service=monitoring"
      - "com.rebuild-link.component=postgres-exporter"
    
    # Network personalizzato
    networks:
      - monitoring

# =============================================================================
# Volumi per persistenza dati
# =============================================================================
volumes:
  # Storage Grafana per dashboard, utenti, configurazioni
  grafana-storage:
    driver: local
    labels:
      - "com.rebuild-link.volume=grafana-storage"
  
  # Posizioni Promtail per tracking file log
  promtail-positions:
    driver: local
    labels:
      - "com.rebuild-link.volume=promtail-positions"

# =============================================================================
# Network personalizzato per isolamento
# =============================================================================
networks:
  monitoring:
    driver: bridge
    labels:
      - "com.rebuild-link.network=monitoring"
