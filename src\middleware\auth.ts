// Middleware di autenticazione JWT per API Next.js + Supabase
// Fornisce la funzione requireAuth per estrarre e validare il token JWT dall'header Authorization.
// Se il token è valido, restituisce l'oggetto user Supabase; altrimenti, una risposta di errore JSON 401.
// Collegato a tutte le rotte protette (es. POST /api/announcements).

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function requireAuth(req: NextRequest) {
  // Demo mode per test E2E (solo in development)
  if (process.env.NODE_ENV === "development") {
    const demoCookie = req.cookies.get('demo-auth');
    if (demoCookie) {
      try {
        const demoUser = JSON.parse(demoCookie.value);
        console.log("🎭 Using demo auth for E2E tests:", demoUser.email);
        return { user: demoUser };
      } catch (error) {
        console.warn("Invalid demo cookie:", error);
      }
    }
  }

  const authHeader = req.headers.get('authorization') ?? '';
  const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;

  if (!token) {
    return { error: NextResponse.json({ error: 'Missing bearer token' }, { status: 401 }) };
  }

  const { data: { user }, error } = await supabase.auth.getUser(token);
  if (error || !user) {
    return { error: NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 }) };
  }

  return { user };
}
