# =============================================================================
# Terraform .gitignore per ReBuild Link Infrastructure
# =============================================================================
# Questo file previene il commit di file sensibili e temporanei di Terraform

# =============================================================================
# File di configurazione sensibili
# =============================================================================

# File con variabili reali (contiene password, token, etc.)
terraform.tfvars
*.tfvars
*.tfvars.json

# File di override locali
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# =============================================================================
# State files di Terraform
# =============================================================================

# State file principale (contiene informazioni sensibili)
terraform.tfstate
terraform.tfstate.*

# Backup dei state file
*.tfstate.backup
*.tfstate.*.backup

# =============================================================================
# Directory e file temporanei
# =============================================================================

# Directory dei provider scaricati
.terraform/
.terraform.lock.hcl

# Directory di crash logs
crash.log
crash.*.log

# Directory di backup
.terraform.tfstate.lock.info

# =============================================================================
# File di log e debug
# =============================================================================

# Log di Terraform
*.log

# File di debug
.terraformrc
terraform.rc

# =============================================================================
# File dell'editor
# =============================================================================

# VS Code
.vscode/

# IntelliJ IDEA
.idea/
*.iml

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# File di sistema
# =============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# =============================================================================
# File di backup e temporanei
# =============================================================================

# Backup files
*.bak
*.backup
*.tmp

# Archive files
*.tar
*.tar.gz
*.zip

# =============================================================================
# Note per sviluppatori
# =============================================================================

# File di note personali
notes.txt
notes.md
TODO.txt
TODO.md

# File di test locali
test.tf
test_*.tf
example_*.tf
