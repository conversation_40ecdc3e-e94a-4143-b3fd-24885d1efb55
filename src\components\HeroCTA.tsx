"use client";
import { But<PERSON> } from '@/components/ui/button';
import { useLocale } from '@/components/LocaleContext';
import Link from 'next/link';

/**
 * Hero section con CTA principale
 * <PERSON>lo grande, copy breve e bottone per inserire annuncio
 */
export default function HeroCTA() {
  const { t } = useLocale();

  return (
    <div className="text-center py-12 mb-12">
      <h1 className="text-4xl md:text-5xl font-heading font-bold text-primary-700 mb-6">
        {t("hero_title")}
      </h1>
      <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
        {t("hero_sub")}
      </p>
      <Link href="/signin">
        <Button
          variant="default"
          className="bg-primary-700 hover:bg-primary-500 text-white px-8 py-3 text-lg"
          aria-label={t("btn_post")}
        >
          {t("btn_post")}
        </Button>
      </Link>
    </div>
  );
}
