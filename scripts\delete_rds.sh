#!/usr/bin/env bash
# ============================================================
# Script: delete_rds.sh
# Scopo : Elimina un'istanza Amazon RDS e le risorse associate
# Autore: ReBuild Link Team
# ------------------------------------------------------------
# Questo script elimina in modo sicuro l'istanza RDS e la
# Security Group associata per evitare costi inutili.
#
# ⚠️  ATTENZIONE: Questa operazione è IRREVERSIBILE!
#
# 👉 Prerequisiti:
#    • AWS CLI configurata (`aws configure`)
#
# ============================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

# -------------------------------
# 1. Parametri personalizzabili
# -------------------------------
DB_INSTANCE_ID="${DB_INSTANCE_ID:-rebuildlink-db}"  # Nome istanza RDS
AWS_REGION="${AWS_REGION:-eu-central-1}"            # Regione AWS
SG_NAME="${SG_NAME:-${DB_INSTANCE_ID}-sg}"          # Security Group

# -------------------------------
# 2. Conferma dall'utente
# -------------------------------
echo "⚠️  ATTENZIONE: Stai per eliminare l'istanza RDS '$DB_INSTANCE_ID'"
echo "   Questa operazione è IRREVERSIBILE e cancellerà tutti i dati!"
echo ""
echo "   Istanza: $DB_INSTANCE_ID"
echo "   Regione: $AWS_REGION"
echo "   Security Group: $SG_NAME"
echo ""
echo "🤔 Sei sicuro di voler procedere? Digita 'DELETE' per confermare:"
read -r confirmation

if [[ "$confirmation" != "DELETE" ]]; then
    echo "❌ Operazione annullata."
    exit 0
fi

# -------------------------------
# 3. Verifica esistenza istanza
# -------------------------------
echo ""
echo "🔍 Verifico l'esistenza dell'istanza..."

if ! aws rds describe-db-instances --db-instance-identifier "$DB_INSTANCE_ID" --region "$AWS_REGION" >/dev/null 2>&1; then
    echo "ℹ️  Istanza $DB_INSTANCE_ID non trovata. Potrebbe essere già stata eliminata."
else
    # -------------------------------
    # 4. Elimina l'istanza RDS
    # -------------------------------
    echo "🗑️  Eliminazione istanza RDS in corso..."
    
    aws rds delete-db-instance \
        --db-instance-identifier "$DB_INSTANCE_ID" \
        --skip-final-snapshot \
        --delete-automated-backups \
        --region "$AWS_REGION"
    
    echo "   Comando di eliminazione inviato ✅"
    echo "   L'istanza impiegherà alcuni minuti per essere completamente rimossa."
fi

# -------------------------------
# 5. Elimina la Security Group
# -------------------------------
echo ""
echo "🔐 Verifico la Security Group $SG_NAME..."

SG_ID=$(aws ec2 describe-security-groups \
          --filters Name=group-name,Values="$SG_NAME" \
          --query 'SecurityGroups[0].GroupId' --output text 2>/dev/null || true)

if [[ -z "$SG_ID" || "$SG_ID" == "None" ]]; then
    echo "ℹ️  Security Group $SG_NAME non trovata."
else
    echo "   Attendo che l'istanza RDS sia completamente eliminata prima di rimuovere la SG..."
    echo "   (Le Security Group non possono essere eliminate se ancora in uso)"
    
    # Attendi che l'istanza sia completamente eliminata
    echo "   Controllo ogni 30 secondi..."
    while aws rds describe-db-instances --db-instance-identifier "$DB_INSTANCE_ID" --region "$AWS_REGION" >/dev/null 2>&1; do
        echo "   Istanza ancora in fase di eliminazione..."
        sleep 30
    done
    
    echo "   Istanza RDS eliminata, rimuovo la Security Group..."
    aws ec2 delete-security-group --group-id "$SG_ID"
    echo "   Security Group $SG_ID eliminata ✅"
fi

# -------------------------------
# 6. Riepilogo finale
# -------------------------------
echo ""
echo "🎉 Pulizia completata!"
echo ""
echo "✅ Risorse eliminate:"
echo "   • Istanza RDS: $DB_INSTANCE_ID"
echo "   • Security Group: $SG_NAME"
echo "   • Backup automatici associati"
echo ""
echo "💰 Non ci saranno più costi per queste risorse."
echo ""
echo "💡 Se in futuro vorrai ricreare l'istanza, usa:"
echo "   ./scripts/create_rds.sh"
