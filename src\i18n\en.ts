const en = {
  common: {
    // Existing
    toggle: "UA / EN",
    comingSoon: "Coming soon…",

    // Navigation & Actions
    skip_to_content: "Skip to content",
    btn_post: "Post Announcement",
    search_btn: "Search",
    search_placeholder: "Search announcements...",

    // Hero Section
    hero_title: "Rebuild Together",
    hero_sub: "Connect with local suppliers, contractors, and talent to rebuild communities in crisis areas",

    // KPI Cards
    kpi_announcements: "Active Announcements",
    kpi_projects: "Projects Completed",
    kpi_companies: "Partner Companies",
    kpi_countries: "Countries Served",

    // Accessibility
    announcement_image_alt: "Announcement image",
    category_label: "Category:",

    // Common UI
    loading: "Loading...",
    error: "Error",
    success: "Success"
  }
};
export default en;
