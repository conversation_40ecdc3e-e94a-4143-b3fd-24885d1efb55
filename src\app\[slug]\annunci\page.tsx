// 📄 src/app/[slug]/annunci/page.tsx
// Lista annunci per tenant con filtri e pagination v2

"use client";
import { useState, useEffect } from "react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import useSWR from "swr";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination";
import AnnouncementCard from "@/components/AnnouncementCard";

const fetcher = (url: string) => fetch(url).then(res => res.json());

/**
 * Pagina lista annunci per un tenant specifico con filtri e pagination
 * URL: /[slug]/annunci (es. /ua/annunci, /it/annunci)
 */
export default function AnnouncementsPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const slug = params.slug as string;

  // Stati locali per i filtri
  const [category, setCategory] = useState(searchParams.get('cat') || 'all');
  const [type, setType] = useState(searchParams.get('type') || 'all');
  const [minPrice, setMinPrice] = useState(searchParams.get('min') || '');
  const [maxPrice, setMaxPrice] = useState(searchParams.get('max') || '');
  const [page, setPage] = useState(parseInt(searchParams.get('page') || '1'));

  // Costruisci URL per SWR
  const buildApiUrl = () => {
    const params = new URLSearchParams();
    params.set('tenant', slug);
    if (category !== 'all') params.set('cat', category);
    if (type !== 'all') params.set('type', type);
    if (minPrice) params.set('min', minPrice);
    if (maxPrice) params.set('max', maxPrice);
    params.set('page', page.toString());
    return `/api/announcements?${params.toString()}`;
  };

  // Fetch con SWR
  const { data, isLoading, error } = useSWR(buildApiUrl(), fetcher);

  // Aggiorna URL quando cambiano i filtri
  const updateUrl = (newPage = 1) => {
    const params = new URLSearchParams();
    if (category !== 'all') params.set('cat', category);
    if (type !== 'all') params.set('type', type);
    if (minPrice) params.set('min', minPrice);
    if (maxPrice) params.set('max', maxPrice);
    if (newPage > 1) params.set('page', newPage.toString());

    const newUrl = `/${slug}/annunci${params.toString() ? '?' + params.toString() : ''}`;
    router.push(newUrl);
  };

  // Applica filtri
  const handleApplyFilters = () => {
    setPage(1);
    updateUrl(1);
  };

  // Cambia pagina
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    updateUrl(newPage);
  };

  // Sincronizza stato con URL params
  useEffect(() => {
    setCategory(searchParams.get('cat') || 'all');
    setType(searchParams.get('type') || 'all');
    setMinPrice(searchParams.get('min') || '');
    setMaxPrice(searchParams.get('max') || '');
    setPage(parseInt(searchParams.get('page') || '1'));
  }, [searchParams]);

  return (
    <div className="container mx-auto px-4 py-8">
      <header className="mb-8">
        <h1 className="text-3xl font-heading font-bold text-primary-700 mb-2">
          Annunci - {slug.toUpperCase()}
        </h1>
        <p className="text-gray-600">
          Trova opportunità di lavoro e progetti nella tua comunità
        </p>
      </header>

      {/* Barra filtri */}
      <div className="bg-white rounded-xl shadow p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
          {/* Select Categoria */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Categoria
            </label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tutte</SelectItem>
                <SelectItem value="motori">Motori</SelectItem>
                <SelectItem value="market">Market</SelectItem>
                <SelectItem value="immobili">Immobili</SelectItem>
                <SelectItem value="lavoro">Lavoro</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Select Tipo */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo
            </label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tutti</SelectItem>
                <SelectItem value="project">Progetto</SelectItem>
                <SelectItem value="job">Lavoro</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Prezzo Min */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prezzo Min
            </label>
            <input
              type="number"
              placeholder="€ Min"
              value={minPrice}
              onChange={(e) => setMinPrice(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Prezzo Max */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prezzo Max
            </label>
            <input
              type="number"
              placeholder="€ Max"
              value={maxPrice}
              onChange={(e) => setMaxPrice(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* Button Applica */}
          <Button
            onClick={handleApplyFilters}
            className="bg-primary-700 hover:bg-primary-500 text-white"
          >
            Applica
          </Button>
        </div>
      </div>

      {/* Lista annunci o skeleton */}
      {isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {[...Array(12)].map((_, i) => (
            <div key={i} className="h-64 bg-neutral-100 animate-pulse rounded-xl" />
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500">Errore nel caricamento degli annunci</p>
        </div>
      ) : (
        <>
          {/* Grid annunci */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {data?.items?.map((announcement: any) => (
              <AnnouncementCard
                key={announcement.id}
                id={announcement.id}
                title={announcement.title}
                type={announcement.type}
                region={announcement.region}
                location={announcement.location}
                description={announcement.description}
                price={announcement.price}
                createdAt={announcement.created_at}
              />
            ))}
          </div>

          {/* Pagination */}
          {data?.totalPages > 1 && (
            <Pagination className="mb-8">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => page > 1 && handlePageChange(page - 1)}
                    className={page <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>

                {[...Array(data.totalPages)].map((_, i) => (
                  <PaginationItem key={i + 1}>
                    <PaginationLink
                      onClick={() => handlePageChange(i + 1)}
                      isActive={page === i + 1}
                      className="cursor-pointer"
                    >
                      {i + 1}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => page < data.totalPages && handlePageChange(page + 1)}
                    className={page >= data.totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}

          {/* Info risultati */}
          {data && (
            <div className="text-center text-sm text-gray-500 mb-8">
              Mostrando {data.items?.length || 0} di {data.total} annunci (Pagina {page} di {data.totalPages})
            </div>
          )}
        </>
      )}

      {/* Pulsante per creare nuovo annuncio */}
      <div className="text-center">
        <Button
          onClick={() => router.push(`/${slug}/annunci/new`)}
          className="bg-primary-700 hover:bg-primary-500 text-white px-8 py-3"
        >
          + Crea Nuovo Annuncio
        </Button>
      </div>
    </div>
  );
}
