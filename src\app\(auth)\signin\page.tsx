// 📄 src/app/(auth)/signin/page.tsx
// Pagina di login con Supabase Auth UI + demo mode per test E2E
// Demo mode: /signin?demo=true usa client-side API call per creare sessione

import SignInClient from "./SignInClient";

/**
 * Server Component che passa i searchParams al client
 */
export default async function SignInPage({
  searchParams
}: {
  searchParams: Promise<{ demo?: string }>
}) {
  const params = await searchParams;

  // Passa demo flag al client component
  return <SignInClient demoMode={params.demo === "true"} />;
}
