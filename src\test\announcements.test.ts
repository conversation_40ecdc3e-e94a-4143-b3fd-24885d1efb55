// Test unitari per l'endpoint GET /api/announcements (src/app/api/announcements/route.ts)
// Questi test verificano che l'handler GET restituisca solo gli annunci del tenant richiesto.
// Si usa una transazione per isolare ogni test. Collegato alla pagina: backend/api-announcements

import { query } from '../lib/db';
import { GET } from '../app/api/announcements/route';
import { NextRequest } from 'next/server';

function mockRequest(tenantId: string) {
  return {
    headers: {
      get: (key: string) => (key === 'x-tenant-id' ? tenantId : undefined),
    },
  } as unknown as NextRequest;
}

describe('GET /api/announcements', () => {
  beforeEach(async () => {
    await query('BEGIN');
  });
  afterEach(async () => {
    await query('ROLLBACK');
  });

  it('restituisce solo gli annunci del tenant richiesto', async () => {
    // Inserisci due annunci, uno per tenant 'test', uno per 'altro'
    await query(
      `INSERT INTO announcements (tenant_id, creator_id, announcement_type, title) VALUES ($1, 1, 'project', 'Annuncio Test')`,
      ['test']
    );
    await query(
      `INSERT INTO announcements (tenant_id, creator_id, announcement_type, title) VALUES ($1, 1, 'job', 'Annuncio Altro')`,
      ['altro']
    );
    // Simula richiesta GET per tenant 'test'
    const req = mockRequest('test');
    const res = await GET(req);
    const json = await res.json();
    expect(Array.isArray(json)).toBe(true);
    expect(json.length).toBe(1);
    expect(json[0].title).toBe('Annuncio Test');
  });
});
