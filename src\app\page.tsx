import { Suspense } from 'react';
import { MessageSquare, TrendingUp, Building2, Globe } from 'lucide-react';
import CategoryMiniCard from '@/components/CategoryMiniCard';
import HeroCTA from '@/components/HeroCTA';
import KpiCard from '@/components/KpiCard';
import { Skeleton } from '@/components/ui/skeleton';

export default function HomePage() {
  const categories = [
    'Motori',
    'Market',
    'Immobili',
    'Lavoro',
    'Servizi',
    'Elettronica',
    'Casa & Giardino',
    'Sport & Tempo Libero'
  ];

  // Dati statici per le metriche - in futuro verranno dall'API
  const stats = [
    { title: "Annunci totali", value: "1 248", icon: <MessageSquare className="w-6 h-6" /> },
    { title: "Progetti finanziati", value: "312", icon: <TrendingUp className="w-6 h-6" /> },
    { title: "Aziende registrate", value: "674", icon: <Building2 className="w-6 h-6" /> },
    { title: "Pa<PERSON> coperti", value: "14", icon: <Globe className="w-6 h-6" /> },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero CTA */}
      <HeroCTA />

      {/* Sezione metriche */}
      <section className="mb-12">
        <h2 className="text-2xl font-heading font-semibold text-center mb-8">
          I nostri numeri
        </h2>
        <Suspense fallback={
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-24" />
            ))}
          </div>
        }>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {stats.map((stat) => (
              <KpiCard
                key={stat.title}
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
              />
            ))}
          </div>
        </Suspense>
      </section>

      {/* Griglia categorie */}
      <section>
        <h2 className="text-2xl font-heading font-semibold text-center mb-8">
          Esplora le categorie
        </h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
          {categories.map((category) => (
            <CategoryMiniCard
              key={category}
              title={category}
            />
          ))}
        </div>
      </section>
    </div>
  );
}
