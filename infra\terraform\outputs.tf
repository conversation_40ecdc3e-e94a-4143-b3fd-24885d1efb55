# =============================================================================
# Outputs Configuration per ReBuild Link Infrastructure
# =============================================================================
# Questo file definisce tutti gli output di Terraform che forniscono
# informazioni utili dopo il deployment dell'infrastruttura.
# =============================================================================

# =============================================================================
# Database Connection Outputs
# =============================================================================

output "DATABASE_URL" {
  description = "Stringa di connessione completa PostgreSQL per l'applicazione"
  value       = "postgres://${var.db_username}:${var.db_password}@${aws_db_instance.postgres.address}:5432/${var.db_name}"
  sensitive   = true
}

output "database_endpoint" {
  description = "Endpoint completo del database RDS (host:porta)"
  value       = aws_db_instance.postgres.endpoint
}

output "database_host" {
  description = "Hostname del database RDS"
  value       = aws_db_instance.postgres.address
}

output "database_port" {
  description = "Porta del database RDS"
  value       = aws_db_instance.postgres.port
}

output "database_name" {
  description = "Nome del database"
  value       = aws_db_instance.postgres.db_name
}

output "database_username" {
  description = "Username per l'accesso al database"
  value       = aws_db_instance.postgres.username
  sensitive   = true
}

# =============================================================================
# Infrastructure Details Outputs
# =============================================================================

output "rds_instance_details" {
  description = "Dettagli completi dell'istanza RDS"
  value = {
    identifier      = aws_db_instance.postgres.identifier
    instance_class  = aws_db_instance.postgres.instance_class
    engine         = aws_db_instance.postgres.engine
    engine_version = aws_db_instance.postgres.engine_version
    allocated_storage = aws_db_instance.postgres.allocated_storage
    storage_type   = aws_db_instance.postgres.storage_type
    multi_az       = aws_db_instance.postgres.multi_az
    publicly_accessible = aws_db_instance.postgres.publicly_accessible
  }
}

output "security_group_details" {
  description = "Dettagli del Security Group per RDS"
  value = {
    id          = aws_security_group.rds_sg.id
    name        = aws_security_group.rds_sg.name
    description = aws_security_group.rds_sg.description
    vpc_id      = aws_security_group.rds_sg.vpc_id
  }
}

output "network_configuration" {
  description = "Configurazione di rete"
  value = {
    vpc_id              = data.aws_vpc.default.id
    security_group_id   = aws_security_group.rds_sg.id
    subnet_group_name   = aws_db_subnet_group.rds_subnet_group.name
    allowed_cidr        = var.allowed_cidr
  }
}

# =============================================================================
# Monitoring and Backup Outputs
# =============================================================================

output "monitoring_configuration" {
  description = "Configurazione del monitoring"
  value = {
    performance_insights_enabled = aws_db_instance.postgres.performance_insights_enabled
    monitoring_interval         = aws_db_instance.postgres.monitoring_interval
    backup_retention_period     = aws_db_instance.postgres.backup_retention_period
    backup_window              = aws_db_instance.postgres.backup_window
    maintenance_window         = aws_db_instance.postgres.maintenance_window
  }
}

output "cloudwatch_alarms" {
  description = "Allarmi CloudWatch configurati"
  value = {
    cpu_alarm         = aws_cloudwatch_metric_alarm.database_cpu.alarm_name
    connections_alarm = aws_cloudwatch_metric_alarm.database_connections.alarm_name
  }
}

# =============================================================================
# Secrets and Configuration Outputs
# =============================================================================

output "deployment_summary" {
  description = "Riepilogo completo del deployment"
  value = {
    # Infrastructure
    aws_region        = var.aws_region
    rds_instance_id   = aws_db_instance.postgres.identifier
    database_endpoint = aws_db_instance.postgres.endpoint
    
    # GitHub Integration
    github_repo       = var.github_repo
    github_secrets    = length(local.github_secrets)
    
    # Vercel Integration
    vercel_project_id = var.vercel_project_id
    vercel_env_vars   = length(local.vercel_env_vars)
    
    # Security
    security_group    = aws_security_group.rds_sg.name
    allowed_cidr      = var.allowed_cidr
    
    # Monitoring
    monitoring_enabled = aws_db_instance.postgres.performance_insights_enabled
    backup_enabled     = aws_db_instance.postgres.backup_retention_period > 0
  }
}

# =============================================================================
# Connection Instructions Output
# =============================================================================

output "connection_instructions" {
  description = "Istruzioni per connettersi al database"
  value = <<-EOT
    
    🎉 Infrastruttura ReBuild Link creata con successo!
    
    📊 Database PostgreSQL:
       Host: ${aws_db_instance.postgres.address}
       Port: ${aws_db_instance.postgres.port}
       Database: ${aws_db_instance.postgres.db_name}
       Username: ${aws_db_instance.postgres.username}
    
    🔗 Connessione diretta:
       psql "postgres://${var.db_username}:${var.db_password}@${aws_db_instance.postgres.address}:5432/${var.db_name}"
    
    🔐 Secrets configurati:
       ✅ GitHub Actions: ${length(local.github_secrets)} secrets
       ✅ Vercel: ${length(local.vercel_env_vars)} environment variables
    
    📈 Monitoring:
       ✅ CloudWatch Enhanced Monitoring attivo
       ✅ Performance Insights abilitato
       ✅ Backup automatici (${aws_db_instance.postgres.backup_retention_period} giorni)
    
    🚀 Prossimi passi:
       1. Esegui migrazioni: ./scripts/run_migrations.sh
       2. Testa connessione: ./scripts/check_rds_status.sh
       3. Deploy applicazione: git push origin main
    
  EOT
}

# =============================================================================
# Local Values per calcoli
# =============================================================================

locals {
  github_secrets = [
    "DATABASE_URL",
    "SUPABASE_URL", 
    "SUPABASE_ANON_KEY",
    "RDS_INSTANCE_ID",
    "AWS_DEFAULT_REGION"
  ]
  
  vercel_env_vars = [
    "DATABASE_URL",
    "NEXT_PUBLIC_SUPABASE_URL",
    "SUPABASE_URL",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY", 
    "SUPABASE_ANON_KEY",
    "NODE_ENV",
    "NEXT_TELEMETRY_DISABLED"
  ]
}
