import { redirect } from "next/navigation";

/**
 * NOTA: In Next.js 15.3.5, i parametri sono asincroni per default
 * Anche se teoricamente dovrebbero essere sincroni nei Server Components,
 * la build fallisce senza Promise<>. Questo potrebbe essere un bug o
 * una configurazione specifica della versione.
 */
export default async function TenantIndex({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  redirect(`/${slug}/annunci`);
}
