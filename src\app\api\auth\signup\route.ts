// 📄 src/app/api/auth/signup/route.ts
// Endpoint Supabase signup – Gestisce la registrazione di nuovi utenti
// Utilizza Zod per la validazione e Supabase per la creazione dell'account

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Inizializzazione client Supabase per server-side
const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Schema di validazione per i dati di registrazione
const signupSchema = z.object({
  email: z.string().email('Email non valida'),
  password: z.string().min(6, 'Password deve essere di almeno 6 caratteri')
});

/**
 * Gestisce la registrazione di nuovi utenti
 * @param req - Request con email e password nel body
 * @returns Response con dati utente o errore
 */
export async function POST(req: NextRequest) {
  try {
    // Parsing del body JSON
    let body: unknown;
    try {
      body = await req.json();
    } catch {
      return NextResponse.json({ error: 'JSON malformato' }, { status: 400 });
    }

    // Validazione dei dati di input
    const result = signupSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json({ 
        error: 'Validazione fallita', 
        details: result.error.errors 
      }, { status: 422 });
    }

    const { email, password } = result.data;

    // Creazione utente con Supabase Admin API
    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true // Conferma automatica per sviluppo
    });

    if (error) {
      // Gestione errori specifici
      if (error.message && error.message.includes('already registered')) {
        return NextResponse.json({ 
          error: 'Email già registrata' 
        }, { status: 409 });
      }
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Successo - ritorna i dati utente (senza password)
    return NextResponse.json({ 
      user: data.user,
      message: 'Utente creato con successo'
    }, { status: 201 });

  } catch (error) {
    console.error('Errore durante la registrazione:', error);
    return NextResponse.json({ 
      error: 'Errore interno del server' 
    }, { status: 500 });
  }
}
