import { test, expect } from "@playwright/test";

/**
 * Test E2E "happy path" completo
 * Flusso: signin → crea annuncio (Project) → appare in lista → chat message → visibile realtime in seconda pagina
 */

const slug = "ua";

test("user can post announcement and chat realtime", async ({ page, context }) => {
  // — SIGNIN (demo mode bypass) —
  console.log("🔐 Testing demo signin flow...");
  await page.goto("/signin?demo=true");

  // Aspetta il redirect alla homepage
  await expect(page).toHaveURL("/", { timeout: 10000 });
  console.log("✅ Demo signin successful");

  // — CREATE ANNOUNCEMENT —
  console.log("📝 Testing announcement creation...");
  await page.goto(`/${slug}/annunci/new`);

  // Aspetta che la pagina si carichi
  await expect(page.getByText("Nuovo Annuncio")).toBeVisible();

  // Compila il form per un Project (tab dovrebbe essere già selezionato)
  await page.getByLabel("Titolo").fill("Test progetto E2E");
  await page.getByLabel("Descrizione").fill("Descrizione lunga di prova per test end-to-end automatizzato con Playwright");
  await page.getByLabel("Regione").fill("Kyiv");
  await page.getByLabel("Budget min (€)").fill("1000");
  await page.getByLabel("Budget max (€)").fill("5000");

  // Submit del form
  await page.getByRole("button", { name: /Pubblica annuncio/ }).click();

  // Aspetta redirect alla lista annunci
  await expect(page).toHaveURL(`/${slug}/annunci`, { timeout: 10000 });
  console.log("✅ Announcement created successfully");

  // — VERIFY ANNOUNCEMENT IN LIST —
  console.log("📋 Testing announcement appears in list...");

  // Aspetta che la lista si carichi e cerca l'annuncio
  await page.waitForLoadState("networkidle");
  const announcementCard = page.locator("text=Test progetto E2E").first();
  await expect(announcementCard).toBeVisible({ timeout: 10000 });
  console.log("✅ Announcement visible in list");

  // — CHAT FUNCTIONALITY —
  console.log("💬 Testing chat functionality...");

  // Clicca sull'annuncio per andare al dettaglio
  await announcementCard.click();

  // Aspetta che la pagina di dettaglio si carichi
  await expect(page.getByText("Chat")).toBeVisible({ timeout: 10000 });

  // Trova il campo di input della chat
  const chatInput = page.getByPlaceholder("Scrivi un messaggio…");
  await expect(chatInput).toBeVisible();

  // Invia un messaggio
  await chatInput.fill("Messaggio E2E test");
  await chatInput.press("Enter");

  // Verifica che il messaggio appaia nella chat
  await expect(page.getByText("Messaggio E2E test")).toBeVisible({ timeout: 10000 });
  console.log("✅ Chat message sent and displayed");

  // — REALTIME TEST CON SECONDA TAB —
  console.log("🔄 Testing realtime sync with second tab...");

  // Apri seconda tab per testare realtime
  const page2 = await context.newPage();
  await page2.goto(page.url());

  // Aspetta che la seconda pagina si carichi
  await expect(page2.getByText("Chat")).toBeVisible({ timeout: 10000 });

  // Verifica che il messaggio sia visibile anche nella seconda tab
  await expect(page2.getByText("Messaggio E2E test")).toBeVisible({ timeout: 10000 });
  console.log("✅ Message visible in second tab");

  // — TEST BIDIREZIONALE —
  console.log("↔️ Testing bidirectional chat...");

  // Invia messaggio dalla seconda tab
  const chatInput2 = page2.getByPlaceholder("Scrivi un messaggio…");
  await chatInput2.fill("Risposta dalla seconda tab");
  await chatInput2.press("Enter");

  // Verifica che il messaggio appaia in entrambe le tab
  await expect(page.getByText("Risposta dalla seconda tab")).toBeVisible({ timeout: 10000 });
  await expect(page2.getByText("Risposta dalla seconda tab")).toBeVisible({ timeout: 10000 });

  console.log("✅ Bidirectional chat working");
  console.log("🎉 Full flow test completed successfully!");
});
