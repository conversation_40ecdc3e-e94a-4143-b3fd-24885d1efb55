# =============================================================================
# Grafana Dashboard Provisioning Configuration
# =============================================================================
# Configurazione per il caricamento automatico delle dashboard in Grafana.
# Le dashboard vengono caricate automaticamente all'avvio del container.
# =============================================================================

apiVersion: 1

# =============================================================================
# Dashboard Providers Configuration
# =============================================================================
providers:
  # ---------------------------------------------------------------------------
  # ReBuild Link Dashboards
  # ---------------------------------------------------------------------------
  - name: 'rebuild-link-dashboards'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    
    # Opzioni di configurazione
    options:
      # Path dove si trovano i file JSON delle dashboard
      path: /etc/grafana/provisioning/dashboards
      
      # Cartella di destinazione in Grafana
      foldersFromFilesStructure: true

# =============================================================================
# Dashboard Files
# =============================================================================
# 
# Le seguenti dashboard vengono caricate automaticamente:
# 
# 1. pg_stat_dashboard.json
#    - PostgreSQL Performance Insights
#    - Query lente, connessioni, statistiche tabelle
#    - Dimensioni database e indici
# 
# 2. Altre dashboard (aggiungi qui se necessario)
#    - Application metrics
#    - System monitoring
#    - Business metrics
# 
# =============================================================================

# =============================================================================
# Istruzioni per aggiungere nuove dashboard
# =============================================================================
# 
# 1. Crea la dashboard in Grafana UI
# 2. Esporta la dashboard (Settings > JSON Model)
# 3. Salva il JSON in questa directory
# 4. Riavvia Grafana o aspetta l'auto-reload (10 secondi)
# 
# Oppure:
# 
# 1. Crea il file JSON manualmente
# 2. Usa la struttura del pg_stat_dashboard.json come template
# 3. Modifica query, pannelli e configurazioni
# 4. Salva nella directory dashboards/
# 
# =============================================================================
