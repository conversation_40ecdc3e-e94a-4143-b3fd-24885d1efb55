# =============================================================================
# Provider Configuration per ReBuild Link Infrastructure
# =============================================================================
# Questo file configura i provider Terraform necessari per gestire:
# - AWS: RDS PostgreSQL, Security Groups, VPC
# - GitHub: Repository secrets per CI/CD
# - Vercel: Environment variables per deployment
# =============================================================================

terraform {
  required_version = ">= 1.6.0"

  required_providers {
    # Provider AWS per gestire risorse cloud (RDS, VPC, Security Groups)
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }

    # Provider GitHub per gestire secrets repository
    github = {
      source  = "integrations/github"
      version = "~> 6.0"
    }

    # Provider Vercel per gestire environment variables
    vercel = {
      source  = "vercel/vercel"
      version = "~> 1.5"
    }
  }

  # Backend configuration (opzionale - per team)
  # backend "s3" {
  #   bucket = "rebuild-link-terraform-state"
  #   key    = "terraform.tfstate"
  #   region = "eu-central-1"
  # }
}

# =============================================================================
# Provider AWS Configuration
# =============================================================================
provider "aws" {
  region = var.aws_region

  # Tags di default per tutte le risorse AWS
  default_tags {
    tags = {
      Project     = "ReBuild Link"
      Environment = "production"
      ManagedBy   = "Terraform"
      Owner       = "DevOps Team"
    }
  }
}

# =============================================================================
# Provider GitHub Configuration
# =============================================================================
provider "github" {
  token = var.github_token
  owner = var.github_owner
}

# =============================================================================
# Provider Vercel Configuration
# =============================================================================
provider "vercel" {
  api_token = var.vercel_token
}
