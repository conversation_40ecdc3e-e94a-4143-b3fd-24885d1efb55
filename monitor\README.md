# 📈 ReBuild Link - Monitoring Stack

Stack di monitoring locale per analisi performance PostgreSQL e log aggregation con Graf<PERSON>, Promtail e PostgreSQL Exporter.

## 🚀 Quick Start

### 1. Configurazione Database

Prima di avviare il monitoring, configura la connessione PostgreSQL:

```bash
# Modifica il file datasource
nano grafana/provisioning/datasources/postgres.yml

# Sostituisci con i tuoi valori RDS:
# - url: your-rds-endpoint.region.rds.amazonaws.com:5432
# - database: rebuildlink
# - user: rebuild_user
# - password: (tramite variabile d'ambiente)
```

### 2. Avvio Stack

```bash
# Avvia tutti i servizi
docker compose up -d

# Verifica stato
docker compose ps

# Visualizza log
docker compose logs -f grafana
```

### 3. Accesso Grafana

- **URL**: http://localhost:3001
- **Username**: admin
- **Password**: admin

## 📊 Dashboard Disponibili

### PostgreSQL Performance Insights
- **🐌 Top 10 Slow Queries**: Query più lente con statistiche esecuzione
- **🔗 Database Connections**: Connessioni attive in tempo reale
- **📊 Table Statistics**: Statistiche utilizzo tabelle
- **💾 Database Size**: Dimensioni tabelle e indici

### Metriche Monitorate
- Query performance (tempo medio, totale, chiamate)
- Connessioni database attive
- Statistiche tabelle (insert, update, delete)
- Dimensioni storage per tabella
- Dead tuples e vacuum status

## 🔧 Configurazione Avanzata

### Variabili d'Ambiente

```bash
# Password PostgreSQL
export POSTGRES_PASSWORD="your-database-password"

# Hostname per Promtail
export HOSTNAME="your-hostname"
```

### Personalizzazione Docker Compose

```yaml
# Aggiungi al docker-compose.yml
environment:
  - POSTGRES_PASSWORD=your-password
  - GF_SECURITY_ADMIN_PASSWORD=your-grafana-password
```

## 📁 Struttura Directory

```
monitor/
├── docker-compose.yml                    # Stack completo
├── promtail.yml                         # Config log aggregation
├── grafana/
│   └── provisioning/
│       ├── datasources/
│       │   └── postgres.yml             # Config PostgreSQL
│       └── dashboards/
│           ├── dashboard.yml            # Config provisioning
│           └── pg_stat_dashboard.json   # Dashboard PostgreSQL
└── README.md                           # Questa documentazione
```

## 🛠️ Servizi Inclusi

### Grafana (Port 3001)
- **Immagine**: grafana/grafana:10.4.1
- **Funzione**: Dashboard e visualizzazione
- **Configurazione**: Auto-provisioning datasource e dashboard
- **Persistenza**: Volume grafana-storage

### Promtail (Internal)
- **Immagine**: grafana/promtail:2.9.3
- **Funzione**: Log aggregation e forwarding
- **Configurazione**: Raccolta log sistema e applicazione
- **Target**: Grafana Loki (se configurato)

### PostgreSQL Exporter (Port 9187)
- **Immagine**: prometheuscommunity/postgres-exporter:v0.15.0
- **Funzione**: Metriche PostgreSQL per Prometheus
- **Endpoint**: http://localhost:9187/metrics
- **Configurazione**: Connessione RDS automatica

## 📈 Utilizzo Dashboard

### Query Performance Analysis
1. Apri dashboard "ReBuild Link – Postgres Insights"
2. Analizza pannello "Top 10 Slow Queries"
3. Identifica query con mean_time alto
4. Ottimizza query o aggiungi indici

### Connection Monitoring
1. Monitora pannello "Database Connections"
2. Verifica che non superi i limiti RDS
3. Implementa connection pooling se necessario

### Storage Analysis
1. Controlla pannello "Database Size by Table"
2. Identifica tabelle che crescono rapidamente
3. Pianifica archiving o partitioning

## 🔍 Troubleshooting

### Grafana non si connette a PostgreSQL
```bash
# Verifica configurazione datasource
docker compose logs grafana

# Testa connessione manualmente
psql "******************************/db"

# Verifica Security Group AWS (porta 5432)
```

### Dashboard vuota o errori
```bash
# Verifica che pg_stat_statements sia abilitato
psql -c "SELECT * FROM pg_stat_statements LIMIT 1;"

# Esegui migration performance
./scripts/run_migrations.sh
```

### Promtail non raccoglie log
```bash
# Verifica permessi directory log
ls -la /var/log/

# Controlla configurazione Promtail
docker compose logs promtail
```

## 🚀 Estensioni

### Aggiungere Prometheus
```yaml
# Aggiungi al docker-compose.yml
prometheus:
  image: prom/prometheus:latest
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml
```

### Aggiungere Loki
```yaml
# Per log centralization completa
loki:
  image: grafana/loki:2.9.3
  ports:
    - "3100:3100"
  command: -config.file=/etc/loki/local-config.yaml
```

### Alert Manager
```yaml
# Per notifiche automatiche
alertmanager:
  image: prom/alertmanager:latest
  ports:
    - "9093:9093"
```

## 📊 Metriche Personalizzate

### Query Custom per Dashboard

```sql
-- Query più chiamate
SELECT query, calls, total_time/calls as avg_time 
FROM pg_stat_statements 
ORDER BY calls DESC LIMIT 10;

-- Tabelle più attive
SELECT schemaname, tablename, n_tup_ins + n_tup_upd + n_tup_del as activity
FROM pg_stat_user_tables 
ORDER BY activity DESC;

-- Indici non utilizzati
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes 
WHERE idx_scan = 0;
```

## 🔐 Sicurezza

### Best Practices
- ✅ Cambia password Grafana di default
- ✅ Usa variabili d'ambiente per credenziali DB
- ✅ Limita accesso rete (firewall/VPN)
- ✅ Abilita SSL per connessioni DB
- ✅ Monitora accessi Grafana

### Configurazione SSL
```yaml
# Per connessioni sicure
environment:
  - DATA_SOURCE_NAME=********************************/db?sslmode=require
```

## 📞 Supporto

Per problemi o domande:
1. Controlla i log: `docker compose logs [service]`
2. Verifica connessioni di rete
3. Consulta documentazione Grafana/Prometheus
4. Testa query PostgreSQL manualmente

---

💡 **Tip**: Usa il refresh automatico (30s) per monitoring in tempo reale durante debug o ottimizzazioni!
