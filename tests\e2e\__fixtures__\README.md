# E2E Test Fixtures

## Demo Authentication

Per i test E2E, utilizziamo un sistema di autenticazione demo che bypassa il normale flusso di login.

### Come Funziona

Il route `/signin?demo=true` deve essere implementato in `src/app/(auth)/signin/page.tsx` per:

1. **Rilevare il parametro demo**: Controlla se `searchParams.demo === "true"`
2. **Creare sessione demo**: Imposta un JWT cookie per un "demo user"
3. **Redirect automatico**: Reindirizza alla homepage (`/`)

### Implementazione Richiesta

```typescript
// src/app/(auth)/signin/page.tsx
export default async function SigninPage({ 
  searchParams 
}: { 
  searchParams: Promise<{ demo?: string }> 
}) {
  const params = await searchParams;
  
  // Demo mode per test E2E
  if (params.demo === "true") {
    // Crea sessione demo user
    const demoUser = {
      id: "demo-user-e2e",
      email: "<EMAIL>",
      name: "Demo User"
    };
    
    // Imposta cookie JWT (implementazione specifica)
    // cookies().set("auth-token", createJWT(demoUser));
    
    // Redirect alla homepage
    redirect("/");
  }
  
  // Normale flusso di signin...
}
```

### Sicurezza

⚠️ **IMPORTANTE**: Il demo mode deve essere attivo **SOLO** in ambiente di sviluppo:

```typescript
if (params.demo === "true" && process.env.NODE_ENV === "development") {
  // Demo logic...
}
```

### Test Flow

Il test E2E segue questo flusso:

1. `GET /signin?demo=true` → Imposta cookie demo
2. `Redirect /` → Homepage con utente autenticato
3. `POST /api/announcements` → Crea annuncio (auth bypass)
4. `GET /{slug}/annunci` → Lista annunci
5. `GET /{slug}/annunci/{id}` → Dettaglio + chat
6. `POST /api/announcements/{id}/messages` → Invia messaggio
7. **Realtime sync** → Verifica sincronizzazione tra tab

### Debugging

Per debuggare i test E2E:

```bash
# Esegui test in modalità headed (con browser visibile)
npx playwright test --headed

# Esegui test specifico
npx playwright test fullFlow.spec.ts

# Debug step-by-step
npx playwright test --debug fullFlow.spec.ts
```
