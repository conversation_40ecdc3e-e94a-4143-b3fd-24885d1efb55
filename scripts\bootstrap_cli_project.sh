#!/usr/bin/env bash
# ==================================================================
# Script: bootstrap_cli_project.sh
# Corso : DevOps per Web App — modulo "From Zero to Cloud"
# Scopo : Con un solo comando da terminale (VS Code) crea:
#         1. repository GitHub
#         2. secrets GitHub Actions
#         3. progetto Vercel
#         4. variabili d'ambiente Vercel (prod + preview)
# ------------------------------------------------------------------
# Dipendenze:
#   • git
#   • GitHub CLI  →  https://cli.github.com/           (gh auth login)
#   • Vercel CLI  →  https://vercel.com/cli            (vercel login)
#   • script set_github_secrets.sh    (già creato)
#   • script setup_vercel_secrets.sh  (già creato)
#
# Variabili richieste (esportale prima di eseguire):
#   DATABASE_URL="******************************/db"
#   SUPABASE_URL="https://<id>.supabase.co"
#   SUPABASE_ANON_KEY="eyJhbGciOi..."
#
# Variabili opzionali:
#   GH_REPO_NAME="my-awesome-app"
#   GH_PRIVATE=true|false           (default: true)
#   VERCEL_PROJECT_NAME="my-awesome-app"
#   VERCEL_TEAM="<team-slug>"       (se usi team diverso dall'account)
# ==================================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

echo "🚀 Bootstrap Completo Progetto DevOps - ReBuild Link"
echo "===================================================="
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 1. CONFIGURAZIONE PARAMETRI
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "1️⃣  Configurazione parametri..."

# Parametri con valori di default intelligenti
GH_REPO_NAME="${GH_REPO_NAME:-$(basename "$(pwd)")}"
GH_PRIVATE="${GH_PRIVATE:-true}"
VERCEL_PROJECT_NAME="${VERCEL_PROJECT_NAME:-$GH_REPO_NAME}"
VERCEL_TEAM_FLAG="${VERCEL_TEAM:+--scope $VERCEL_TEAM}"

echo "   📦 Repository GitHub: $GH_REPO_NAME"
echo "   🔒 Visibilità: $([[ "$GH_PRIVATE" == true ]] && echo "Privato" || echo "Pubblico")"
echo "   🚀 Progetto Vercel: $VERCEL_PROJECT_NAME"
echo "   👥 Team Vercel: ${VERCEL_TEAM:-"Account personale"}"

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 2. VALIDAZIONE PREREQUISITI
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "2️⃣  Validazione prerequisiti..."

# Controlla comandi necessari
required_commands=("git" "gh" "vercel")
missing_commands=()

for cmd in "${required_commands[@]}"; do
    if command -v "$cmd" >/dev/null 2>&1; then
        echo "   ✅ $cmd disponibile"
    else
        echo "   ❌ $cmd non trovato"
        missing_commands+=("$cmd")
    fi
done

if [ ${#missing_commands[@]} -gt 0 ]; then
    echo ""
    echo "❌ Comandi mancanti: ${missing_commands[*]}"
    echo ""
    echo "📦 Installazione richiesta:"
    for cmd in "${missing_commands[@]}"; do
        case "$cmd" in
            "git")
                echo "   • Git: https://git-scm.com/downloads"
                ;;
            "gh")
                echo "   • GitHub CLI: https://cli.github.com/"
                echo "     - macOS: brew install gh"
                echo "     - Ubuntu: sudo apt install gh"
                echo "     - Windows: winget install GitHub.cli"
                ;;
            "vercel")
                echo "   • Vercel CLI: npm i -g vercel"
                ;;
        esac
    done
    exit 1
fi

# Controlla autenticazione GitHub
if ! gh auth status >/dev/null 2>&1; then
    echo "   ❌ GitHub CLI non autenticato"
    echo ""
    echo "🔑 Esegui prima: gh auth login"
    echo "   Segui le istruzioni per autenticarti con GitHub"
    exit 1
else
    gh_user=$(gh api user --jq .login)
    echo "   ✅ GitHub CLI autenticato come: $gh_user"
fi

# Controlla autenticazione Vercel
if ! vercel whoami >/dev/null 2>&1; then
    echo "   ❌ Vercel CLI non autenticato"
    echo ""
    echo "🔑 Esegui prima: vercel login"
    echo "   Si aprirà il browser per l'autenticazione"
    exit 1
else
    vercel_user=$(vercel whoami)
    echo "   ✅ Vercel CLI autenticato come: $vercel_user"
fi

# Controlla variabili d'ambiente richieste
required_vars=("DATABASE_URL" "SUPABASE_URL" "SUPABASE_ANON_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [[ -n "${!var:-}" ]]; then
        value="${!var}"
        preview="${value:0:30}..."
        echo "   ✅ $var configurata ($preview)"
    else
        echo "   ❌ $var non impostata"
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "❌ Variabili d'ambiente mancanti: ${missing_vars[*]}"
    echo ""
    echo "💡 Esporta le variabili richieste:"
    echo "   export DATABASE_URL=\"******************************/db\""
    echo "   export SUPABASE_URL=\"https://your-project.supabase.co\""
    echo "   export SUPABASE_ANON_KEY=\"eyJhbGciOi...\""
    echo ""
    echo "🔍 Per ottenere DATABASE_URL: ./scripts/check_rds_status.sh"
    echo "🔍 Per ottenere Supabase vars: Dashboard Supabase > Settings > API"
    exit 1
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 3. CREAZIONE/COLLEGAMENTO REPOSITORY GITHUB
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "3️⃣  Setup repository GitHub..."

# Controlla se il repository esiste già
if gh repo view "$gh_user/$GH_REPO_NAME" >/dev/null 2>&1; then
    echo "   📦 Repository $gh_user/$GH_REPO_NAME già esistente"
    
    # Verifica se abbiamo un remote origin
    if ! git remote get-url origin >/dev/null 2>&1; then
        echo "   🔗 Collegamento remote origin..."
        git remote add origin "https://github.com/$gh_user/$GH_REPO_NAME.git"
    fi
else
    echo "   📦 Creazione repository GitHub..."
    
    # Inizializza git se necessario
    if [[ ! -d ".git" ]]; then
        echo "   🔧 Inizializzazione repository Git..."
        git init
        git add .
        git commit -m "feat: initial commit with complete DevOps setup"
    fi
    
    # Crea repository GitHub
    privacy_flag=$([[ "$GH_PRIVATE" == true ]] && echo "--private" || echo "--public")
    gh repo create "$GH_REPO_NAME" $privacy_flag \
        --source=. --remote=origin --push
    
    echo "   ✅ Repository creato e push iniziale completato"
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 4. CONFIGURAZIONE GITHUB ACTIONS SECRETS
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "4️⃣  Configurazione GitHub Actions secrets..."

echo "   🔑 Esecuzione script set_github_secrets.sh..."
if ./scripts/set_github_secrets.sh; then
    echo "   ✅ GitHub Actions secrets configurati"
else
    echo "   ❌ Errore nella configurazione GitHub secrets"
    exit 1
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 5. SETUP PROGETTO VERCEL
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "5️⃣  Setup progetto Vercel..."

# Prova a collegare progetto esistente
echo "   🔗 Tentativo collegamento progetto esistente..."
if vercel link --yes --project "$VERCEL_PROJECT_NAME" $VERCEL_TEAM_FLAG >/dev/null 2>&1; then
    echo "   ✅ Progetto Vercel collegato: $VERCEL_PROJECT_NAME"
else
    echo "   📦 Progetto non trovato, creazione nuovo progetto..."
    
    # Crea nuovo progetto Vercel
    if vercel project add --name "$VERCEL_PROJECT_NAME" --framework nextjs --yes $VERCEL_TEAM_FLAG; then
        echo "   ✅ Progetto Vercel creato: $VERCEL_PROJECT_NAME"
        
        # Collega il progetto appena creato
        vercel link --yes --project "$VERCEL_PROJECT_NAME" $VERCEL_TEAM_FLAG
        echo "   🔗 Progetto collegato alla directory corrente"
    else
        echo "   ❌ Errore nella creazione progetto Vercel"
        exit 1
    fi
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 6. CONFIGURAZIONE VARIABILI D'AMBIENTE VERCEL
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "6️⃣  Configurazione variabili d'ambiente Vercel..."

# Funzione helper per aggiungere variabili d'ambiente
add_env_var() {
    local var_name="$1"
    local var_value="$2"
    local environment="$3"
    
    echo "   🌱 Aggiungendo $var_name per ambiente $environment..."
    if echo "$var_value" | vercel env add "$var_name" "$environment" --yes $VERCEL_TEAM_FLAG >/dev/null 2>&1; then
        echo "   ✅ $var_name configurata per $environment"
    else
        echo "   ⚠️  $var_name già esistente per $environment (skip)"
    fi
}

# Configura variabili per production e preview
environments=("production" "preview")

for env in "${environments[@]}"; do
    echo "   📋 Configurazione ambiente: $env"
    add_env_var "DATABASE_URL" "$DATABASE_URL" "$env"
    add_env_var "SUPABASE_URL" "$SUPABASE_URL" "$env"
    add_env_var "SUPABASE_ANON_KEY" "$SUPABASE_ANON_KEY" "$env"
done

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 7. CONFIGURAZIONE SECRETS VERCEL PER CI/CD
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "7️⃣  Configurazione secrets Vercel per CI/CD..."

echo "   🔐 Esecuzione script setup_vercel_secrets.sh..."
if ./scripts/setup_vercel_secrets.sh; then
    echo "   ✅ Secrets Vercel per CI/CD configurati"
else
    echo "   ❌ Errore nella configurazione secrets Vercel"
    exit 1
fi

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# 8. RIEPILOGO FINALE E PROSSIMI PASSI
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo ""
echo "🎉 Bootstrap CLI completato con successo!"
echo ""
echo "📋 Riepilogo configurazione:"
echo "   • Repository GitHub: https://github.com/$gh_user/$GH_REPO_NAME"
echo "   • Progetto Vercel: $VERCEL_PROJECT_NAME"
echo "   • GitHub Actions: Secrets configurati"
echo "   • Vercel: Variabili d'ambiente configurate"
echo "   • CI/CD: Pipeline completa attiva"
echo ""
echo "🚀 Prossimi passi:"
echo "   1. Verifica configurazione:"
echo "      gh secret list --repo $gh_user/$GH_REPO_NAME"
echo "      vercel env ls $VERCEL_TEAM_FLAG"
echo ""
echo "   2. Testa la pipeline CI/CD:"
echo "      git add ."
echo "      git commit -m \"feat: test CI/CD pipeline\""
echo "      git push origin main"
echo ""
echo "   3. Monitora il deploy:"
echo "      https://github.com/$gh_user/$GH_REPO_NAME/actions"
echo "      https://vercel.com/dashboard"
echo ""
echo "💡 La pipeline eseguirà automaticamente:"
echo "   • Lint del codice"
echo "   • Test dell'applicazione"
echo "   • Build di produzione"
echo "   • Automigrazioni database"
echo "   • Deploy su Vercel"
echo ""
echo "🔗 Documentazione completa: cat README.md"
