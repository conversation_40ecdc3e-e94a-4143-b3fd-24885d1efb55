"use client";
import { ReactNode } from 'react';
import { useLocale } from '@/components/LocaleContext';

interface CategoryMiniCardProps {
  title: string;
  icon?: ReactNode;
}

/**
 * Mini card per categoria con design compatto
 * Hover effect da primary-700 a primary-500
 */
export default function CategoryMiniCard({ title, icon }: CategoryMiniCardProps) {
  const { t } = useLocale();

  return (
    <div
      className="rounded-xl bg-primary-700 hover:bg-primary-500 text-white flex flex-col items-center justify-center h-32 w-full transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
      role="button"
      tabIndex={0}
      aria-label={`${t("category_label")} ${title}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          // Handle click action here
        }
      }}
    >
      {icon && (
        <div className="mb-2 text-2xl" aria-hidden="true">
          {icon}
        </div>
      )}
      <h3 className="font-heading font-semibold text-center text-sm">
        {title}
      </h3>
    </div>
  );
}
