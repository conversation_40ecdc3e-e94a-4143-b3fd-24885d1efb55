#!/usr/bin/env bash
# =============================================================
# Script: run_migrations.sh
# Corso : DevOps per Web App – lezione "Automigrazioni SQL"
# Scopo  : Eseguire automaticamente tutti i file SQL dentro
#          la cartella migrations/ contro l'istanza Postgres
#          in cloud (AWS RDS) usando psql.
# -------------------------------------------------------------
# Come funziona:
#   1. Legge la variabile d'ambiente DATABASE_URL
#      (es. **********************************/db).
#   2. Ordina i file *.sql per nome (timestamp → ordine logico).
#   3. Esegue ogni script con psql, fermandosi se uno fallisce.
#
# Prerequisiti:
#   • postgresql-client installato (psql disponibile nel PATH)
#   • Variabile DATABASE_URL esportata (GitHub Secrets in CI)
#   • Cartella migrations/ con i tuoi file SQL
# =============================================================

# ‼️ Uscire immediatamente in caso di errore
set -euo pipefail

echo "🗄️ Sistema Automigrazioni Database - ReBuild Link"
echo "================================================="
echo ""

# -----------------------------
# 1. Validazione prerequisiti
# -----------------------------
echo "1️⃣  Validazione prerequisiti..."

# Controlla che DATABASE_URL sia impostata
if [[ -z "${DATABASE_URL:-}" ]]; then
    echo "❌ DATABASE_URL non impostata!"
    echo ""
    echo "💡 Esporta la variabile prima di eseguire lo script:"
    echo "   export DATABASE_URL='**********************************/db'"
    echo ""
    echo "🔍 Per ottenere la DATABASE_URL:"
    echo "   ./scripts/check_rds_status.sh"
    exit 1
fi

# Controlla che psql sia disponibile
if ! command -v psql >/dev/null 2>&1; then
    echo "❌ PostgreSQL client (psql) non trovato!"
    echo ""
    echo "📦 Installazione:"
    echo "   • Ubuntu/Debian: sudo apt-get install postgresql-client"
    echo "   • macOS: brew install postgresql"
    echo "   • Windows: Installa PostgreSQL da postgresql.org"
    exit 1
fi

# Controlla che la cartella migrations esista
if [[ ! -d "migrations" ]]; then
    echo "❌ Cartella 'migrations' non trovata!"
    echo ""
    echo "📁 Crea la cartella migrations:"
    echo "   mkdir -p migrations"
    echo ""
    echo "💡 Aggiungi i tuoi file SQL con naming convention:"
    echo "   migrations/001_initial_schema.sql"
    echo "   migrations/002_add_users_table.sql"
    echo "   migrations/YYYYMMDD_description.sql"
    exit 1
fi

echo "   ✅ DATABASE_URL configurata"
echo "   ✅ PostgreSQL client disponibile"
echo "   ✅ Cartella migrations presente"

# -----------------------------
# 2. Informazioni connessione
# -----------------------------
echo ""
echo "2️⃣  Informazioni connessione..."

# Estrai informazioni dalla DATABASE_URL (senza mostrare password)
DB_HOST=$(echo "$DATABASE_URL" | sed -n 's/.*@\([^:]*\):.*/\1/p')
DB_PORT=$(echo "$DATABASE_URL" | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo "$DATABASE_URL" | sed -n 's/.*\/\([^?]*\).*/\1/p')
DB_USER=$(echo "$DATABASE_URL" | sed -n 's/.*:\/\/\([^:]*\):.*/\1/p')

echo "   🏠 Host: $DB_HOST"
echo "   🚪 Porta: $DB_PORT"
echo "   📊 Database: $DB_NAME"
echo "   👤 Utente: $DB_USER"

# -----------------------------
# 3. Test connessione database
# -----------------------------
echo ""
echo "3️⃣  Test connessione database..."

if timeout 10 psql "$DATABASE_URL" -c "SELECT 1;" >/dev/null 2>&1; then
    echo "   ✅ Connessione al database riuscita"
else
    echo "   ❌ Impossibile connettersi al database!"
    echo ""
    echo "🔍 Possibili cause:"
    echo "   • DATABASE_URL non corretta"
    echo "   • Istanza RDS non attiva"
    echo "   • Security Group non permette connessioni"
    echo "   • Credenziali non valide"
    echo ""
    echo "💡 Verifica stato RDS:"
    echo "   ./scripts/check_rds_status.sh"
    exit 1
fi

# -----------------------------
# 4. Scansione file migrazioni
# -----------------------------
echo ""
echo "4️⃣  Scansione file migrazioni..."

# Trova tutti i file .sql nella cartella migrations
migration_files=($(find migrations -name "*.sql" -type f | sort))

if [[ ${#migration_files[@]} -eq 0 ]]; then
    echo "   ⚠️  Nessun file di migrazione trovato in migrations/"
    echo ""
    echo "💡 Aggiungi file SQL con naming convention:"
    echo "   migrations/001_initial_schema.sql"
    echo "   migrations/002_add_users_table.sql"
    echo "   migrations/$(date +%Y%m%d)_description.sql"
    echo ""
    echo "✅ Nessuna migrazione da eseguire"
    exit 0
fi

echo "   📋 Trovati ${#migration_files[@]} file di migrazione:"
for file in "${migration_files[@]}"; do
    echo "      • $(basename "$file")"
done

# -----------------------------
# 5. Creazione tabella tracking (se non esiste)
# -----------------------------
echo ""
echo "5️⃣  Setup sistema tracking migrazioni..."

# Crea tabella per tracciare migrazioni eseguite
psql "$DATABASE_URL" -v ON_ERROR_STOP=1 << 'EOF'
CREATE TABLE IF NOT EXISTS schema_migrations (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) UNIQUE NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    checksum VARCHAR(64)
);
EOF

echo "   ✅ Tabella schema_migrations pronta"

# -----------------------------
# 6. Esecuzione migrazioni
# -----------------------------
echo ""
echo "6️⃣  Esecuzione migrazioni..."

migrations_applied=0
migrations_skipped=0

for sql_file in "${migration_files[@]}"; do
    filename=$(basename "$sql_file")
    
    # Controlla se la migrazione è già stata applicata
    already_applied=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM schema_migrations WHERE filename = '$filename';" | tr -d ' ')
    
    if [[ "$already_applied" -gt 0 ]]; then
        echo "   ⏭️  $filename (già applicata)"
        ((migrations_skipped++))
        continue
    fi
    
    echo "   ➡️  Applicando $filename..."
    
    # Calcola checksum del file per tracking
    if command -v sha256sum >/dev/null 2>&1; then
        checksum=$(sha256sum "$sql_file" | cut -d' ' -f1)
    elif command -v shasum >/dev/null 2>&1; then
        checksum=$(shasum -a 256 "$sql_file" | cut -d' ' -f1)
    else
        checksum="unknown"
    fi
    
    # Esegui la migrazione in una transazione
    if psql "$DATABASE_URL" -v ON_ERROR_STOP=1 << EOF
BEGIN;
-- Esegui il contenuto del file SQL
\i $sql_file
-- Registra la migrazione come applicata
INSERT INTO schema_migrations (filename, checksum) VALUES ('$filename', '$checksum');
COMMIT;
EOF
    then
        echo "   ✅ $filename applicata con successo"
        ((migrations_applied++))
    else
        echo "   ❌ Errore nell'applicazione di $filename"
        echo ""
        echo "🔍 La migrazione è stata annullata (ROLLBACK automatico)"
        echo "💡 Correggi il file SQL e riprova"
        exit 1
    fi
done

# -----------------------------
# 7. Riepilogo finale
# -----------------------------
echo ""
echo "📊 Riepilogo migrazioni:"
echo "   • File trovati: ${#migration_files[@]}"
echo "   • Migrazioni applicate: $migrations_applied"
echo "   • Migrazioni saltate: $migrations_skipped"
echo ""

if [[ $migrations_applied -gt 0 ]]; then
    echo "🎉 Migrazioni completate con successo!"
    echo ""
    echo "📋 Per vedere lo storico migrazioni:"
    echo "   psql \"$DATABASE_URL\" -c \"SELECT filename, applied_at FROM schema_migrations ORDER BY applied_at;\""
else
    echo "✅ Database già aggiornato, nessuna migrazione necessaria"
fi

echo ""
echo "🔗 Per maggiori informazioni: cat scripts/README.md"
