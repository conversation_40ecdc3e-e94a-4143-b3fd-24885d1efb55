import { test, expect } from "@playwright/test";

/**
 * Test semplici per verificare che il setup E2E funzioni
 */

test.describe("Demo Authentication", () => {
  test("demo signin redirects to homepage", async ({ page }) => {
    console.log("🧪 Testing demo signin redirect...");

    // Vai alla pagina demo signin
    await page.goto("/signin?demo=true");

    // Aspetta il redirect alla homepage
    await expect(page).toHaveURL("/", { timeout: 10000 });

    console.log("✅ Demo signin redirect test passed!");
  });

  test("normal signin page loads correctly", async ({ page }) => {
    console.log("🧪 Testing normal signin page...");

    // Vai alla pagina signin normale
    await page.goto("/signin");

    // Verifica che la pagina signin si carichi
    await expect(page.getByText("ReBuild Link")).toBeVisible();
    await expect(page.getByText("Accedi alla piattaforma")).toBeVisible();

    console.log("✅ Normal signin page test passed!");
  });
});

test.describe("Basic Navigation", () => {
  test("can navigate to tenant pages", async ({ page }) => {
    console.log("🧪 Testing tenant navigation...");

    // Testa navigazione diretta alle pagine tenant
    await page.goto("/ua/annunci");
    await expect(page).toHaveURL("/ua/annunci");

    await page.goto("/it/annunci");
    await expect(page).toHaveURL("/it/annunci");

    console.log("✅ Tenant navigation test passed!");
  });
});
